import { _decorator, Component, Node, Button, Sprite, Vec3, tween, EventHandler, Color, UITransform, UIOpacity, SpriteFrame } from 'cc';
import { UIButton } from '../../eventSystem/UIButton';
import { Signal } from '../../eventSystem/Signal';
import { GameManager } from '../../core/GameManager';
import { GameState } from '../../enum/Enums';
import { SoundManager } from '../../core/SoundManager';
const { ccclass, property } = _decorator;

@ccclass('Tile')
export class Tile extends Component {
    @property(Sprite) tileSprite: Sprite = null!;
    @property(Sprite) iconSprite: Sprite = null!;
    @property(Button) tileButton: Button = null!;
    @property(UIButton) titleBtn: UIButton = null!;
    @property(Node) parentVisual: Node = null!;
    @property([SpriteFrame]) tileSprites: SpriteFrame[] = [];
    @property(Node) glowEffect: Node = null!;

    private _tileType: number = 0;
    private _isBlocked: boolean = false;
    private _isDisplay: boolean = true;
    private _originalPosition: Vec3 = new Vec3();
    private _isSelected: boolean = false;
    private _gridX: number = 0;
    private _gridY: number = 0;
    private _blockingParents: Tile[] = [];
    private _blockingChildren: Tile[] = [];
    private _isInTray: boolean = false;

    public onTileTutTap: Signal<Tile> = new Signal();

    // Getters and Setters
    get tileType(): number { return this._tileType; }
    set tileType(value: number) { this._tileType = value; this.updateTileSprite(); }

    get isBlocked(): boolean { return this._isBlocked; }
    set isBlocked(value: boolean) { this._isBlocked = value; this.updateInteractable(); }

    get isDisplay(): boolean { return this._isDisplay; }
    set isDisplay(value: boolean) { this._isDisplay = value; this.updateVisibility(); }

    get gridX(): number { return this._gridX; }
    set gridX(value: number) { this._gridX = value; }

    get gridY(): number { return this._gridY; }
    set gridY(value: number) { this._gridY = value; }

    get blockingParents(): Tile[] { return this._blockingParents; }
    get blockingChildren(): Tile[] { return this._blockingChildren; }
    get hasBlockingParents(): boolean { return this._blockingParents.length > 0; }

    get isInTray(): boolean { return this._isInTray; }
    set isInTray(value: boolean) {
        this._isInTray = value;
        this.updateTrayState();
        if (value) this.updateBlockingChildrenWhenMovedToTray();
    }

    // Blocking management methods
    addBlockingParent(tile: Tile): void {
        if (this._blockingParents.indexOf(tile) === -1) {
            this._blockingParents.push(tile);
        }
    }

    removeBlockingParent(tile: Tile): void {
        const index = this._blockingParents.indexOf(tile);
        if (index > -1) this._blockingParents.splice(index, 1);
    }

    clearBlockingParents(): void { this._blockingParents = []; }

    addBlockingChild(tile: Tile): void {
        if (this._blockingChildren.indexOf(tile) === -1) {
            this._blockingChildren.push(tile);
        }
    }

    removeBlockingChild(tile: Tile): void {
        const index = this._blockingChildren.indexOf(tile);
        if (index > -1) this._blockingChildren.splice(index, 1);
    }

    clearBlockingChildren(): void { this._blockingChildren = []; }





    public static onTileTap: Signal<Tile> = new Signal();


    onLoad() {
        this._originalPosition = this.node.position.clone();
        this.hideGlowEffect();
    }

    start() {
        this.updateTileSprite();
        this.updateInteractable();
        this.titleBtn.InteractedEvent.on(this.onTileClicked, this);
    }

    // Glow effect methods
    public showGlowEffect() { this.glowEffect.active = true; }
    public hideGlowEffect() { this.glowEffect.active = false; }

    // Initialization methods
    initTile(tileType: number, x: number, y: number) {
        this._tileType = tileType;
        this.node.setPosition(x, y, 0);
        this._originalPosition = this.node.position.clone();
        this.updateTileSprite();
    }

    initTileWithGrid(tileType: number, gridX: number, gridY: number, worldX: number, worldY: number) {
        this._tileType = tileType;
        this._gridX = gridX;
        this._gridY = gridY;
        this.node.setPosition(worldX, worldY, 0);
        this._originalPosition = this.node.position.clone();
        this.updateTileSprite();
    }

    public setupView(data: any) {
        // Set tile type
        this.tileType = data.type;

        // Set blocked status from data
        this._isBlocked = data.isBlock;

        // Set display status from data
        this._isDisplay = data.isDisplay;

        // Set grid coordinates if available
        if (data.x !== undefined) {
            this._gridX = data.x;
        }
        if (data.y !== undefined) {
            this._gridY = data.y;
        }

        // Update visibility
        this.updateVisibility();

        // Update sprite based on type
        this.updateTileSprite();

        console.log(`Tile setupView: type=${data.type}, isDisplay=${data.isDisplay}, isBlock=${data.isBlock}, grid(${this._gridX}, ${this._gridY})`);
    }

    /**
     * Cập nhật sprite của tile dựa trên tileType
     */
    private updateTileSprite() {
        if (this.tileSprites?.length > 0 && this.iconSprite) {
            const spriteIndex = this._tileType % this.tileSprites.length;
            this.iconSprite.spriteFrame = this.tileSprites[spriteIndex];
        }
    }

    private updateInteractable() {
        if (this.tileButton) {
            this.tileButton.interactable = !this._isBlocked;
        }

        const color = this._isBlocked ? Color.GRAY : Color.WHITE;
        if (this.tileSprite) this.tileSprite.color = color;
        if (this.iconSprite) this.iconSprite.color = color;
    }

    private updateVisibility() {
        if (this.parentVisual) {
            this.parentVisual.active = this._isDisplay;
        }
    }

    private updateTrayState() {
        if (this._isInTray) {
            this.node.scale = new Vec3(0.8, 0.8, 1);
            if (this.tileSprite) this.tileSprite.color = Color.WHITE;
            if (this.iconSprite) this.iconSprite.color = Color.WHITE;
        } else {
            this.node.scale = Vec3.ONE.clone();
            this.updateInteractable();
        }
    }

    private updateBlockingChildrenWhenMovedToTray(): void {
        this._blockingChildren.forEach(childTile => {
            childTile.removeBlockingParent(this);
            if (childTile.blockingParents.length <= 0) {
                childTile.isBlocked = false;
            }
        });
        this.clearBlockingChildren();
    }

    /**
     * Xử lý sự kiện click tile
     */
    onTileClicked() {
        // console.log('Tile clicked:', this.getDebugInfo());
        if (this._isBlocked || !this._isDisplay || this._isInTray || GameManager.Instance.currentGameState !== GameState.Play) {
            return;
        }

        Tile.onTileTap?.trigger(this);
        this.onTileTutTap?.trigger(this);

        SoundManager.Instance.playSfx(SoundManager.Instance.Button_Click);

        this.hideGlowEffect();

    }

    /**
     * Đánh dấu tile được chọn
     */
    setSelected(selected: boolean) {
        this._isSelected = selected;

        if (selected) {
            // Animation khi được chọn
            this.playSelectAnimation();
        }
    }

    /**
     * Animation khi tile được chọn
     */
    private playSelectAnimation() {
        const originalScale = this.node.scale.clone();

        tween(this.node)
            .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
            .to(0.1, { scale: originalScale })
            .start();
    }

    /**
     * Animation di chuyển tile đến vị trí mới (sử dụng world position)
     */
    moveTo(targetWorldPosition: Vec3, duration: number = 0.3): Promise<void> {
        return new Promise((resolve) => {
            // Chuyển đổi world position thành local position
            const parent = this.node.parent;
            if (!parent) {
                console.warn("Tile node has no parent, cannot convert world position");
                resolve();
                return;
            }

            const parentUITransform = parent.getComponent(UITransform);
            if (!parentUITransform) {
                console.warn("Parent node has no UITransform component");
                resolve();
                return;
            }

            // Chuyển đổi từ world position sang local position của parent
            const localPosition = parentUITransform.convertToNodeSpaceAR(targetWorldPosition);

            tween(this.node)
                .to(duration, {
                    position: localPosition,
                    scale: new Vec3(0.8, 0.8, 1)
                })
                .call(() => {
                    resolve();
                })
                .start();
        });
    }

    /**
     * Animation trở về vị trí ban đầu (cho chức năng Undo)
     */
    returnToOriginalPosition(duration: number = 0.3): Promise<void> {
        return new Promise((resolve) => {
            tween(this.node)
                .to(duration, { position: this._originalPosition })
                .call(() => {
                    this._isSelected = false;
                    resolve();
                })
                .start();
        });
    }

    /**
     * Animation biến mất (khi match thành công)
     */
    playDisappearAnimation(delay: number = 0): Promise<void> {
        return new Promise((resolve) => {
            tween(this.node)
                .delay(delay)
                .to(0.2, { scale: new Vec3(1.2, 1.2, 1) })
                .to(0.2, { scale: new Vec3(0, 0, 0) })
                .call(() => {
                    resolve();
                })
                .start();
        });
    }

    resetTile() {
        this._tileType = 0;
        this._isBlocked = false;
        this._isDisplay = true;
        this._isSelected = false;
        this._isInTray = false;
        this._gridX = 0;
        this._gridY = 0;
        this.clearBlockingParents();
        this.clearBlockingChildren();
        this.node.scale = Vec3.ONE.clone();
        this.node.position = Vec3.ZERO.clone();

        if (this.tileSprite) this.tileSprite.color = Color.WHITE;
        if (this.iconSprite) this.iconSprite.color = Color.WHITE;
    }

    /**
     * Kiểm tra xem tile có thể được chọn không
     */
    canBeSelected(): boolean {
        return !this._isBlocked && !this._isSelected;
    }



    /**
     * Lấy thông tin debug của tile
     */
    getDebugInfo(): string {
        const parentsInfo = this._blockingParents.length > 0 ?
            `Parents[${this._blockingParents.map(p => `(${p.gridX},${p.gridY})`).join(',')}]` :
            'NoParents';
        const childrenInfo = this._blockingChildren.length > 0 ?
            `Children[${this._blockingChildren.map(c => `(${c.gridX},${c.gridY})`).join(',')}]` :
            'NoChildren';
        const trayStatus = this._isInTray ? 'InTray' : 'OnBoard';
        return `Tile[Type:${this._tileType}, Display:${this._isDisplay}, Blocked:${this._isBlocked}, Selected:${this._isSelected}, Grid(${this._gridX}, ${this._gridY}), ${parentsInfo}, ${childrenInfo}, ${trayStatus}]`;
    }
}


