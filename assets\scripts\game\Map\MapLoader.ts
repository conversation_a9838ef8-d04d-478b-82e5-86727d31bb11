import { _decorator, Component, instantiate, JsonAsset, Node, Prefab, Vec3, Intersection2D, Rect, UITransform, Layout, CCInteger } from 'cc';
import { Tile } from '../Tile/Tile';
import { GameManager } from '../../core/GameManager';
import { GameState } from '../../enum/Enums';
import { SoundManager } from '../../core/SoundManager';
import { Tutorial } from '../../ui/Tutorial';
const { ccclass, property } = _decorator;

@ccclass('MapLoader')
export class MapLoader extends Component {
    @property(Prefab) private tilePrefab: Prefab = null!;
    @property(Prefab) private layerPrefab: Prefab = null!;
    @property(JsonAsset) private levelData: JsonAsset = null!;
    @property(Tutorial) private tutorial: Tutorial = null!;
    @property(CCInteger) private currentLevel = 0;

    private static readonly TILE_SIZE: number = 114;
    private allTileComponents: { component: Tile, layerIndex: number, tileData: any }[] = [];

    get CurrentLevel(): number { return this.currentLevel; }
    set CurrentLevel(value: number) { this.currentLevel = value; }

    onLoad() {
        GameManager.Instance.onStateChange.on(this.onGameStateChange, this);
    }

    private onGameStateChange(state: GameState) {
        if (state === GameState.Prepare) {
            
            SoundManager.Instance.playBgm(SoundManager.Instance.BGM);
            this.loadLevel();
            this.tutorial.initialize(this);
        }
    }

    // Level management methods
    public loadLevelByIndex(levelIndex: number): void {
        if (this.isLevelExists(levelIndex)) {
            this.currentLevel = levelIndex;
            this.reloadCurrentLevel();
        } else {
            console.error(`MapLoader: Level ${levelIndex} not found. Available: 0-${this.getAvailableLevelsCount() - 1}`);
        }
    }

    public loadNextLevel(): void {
        if (this.isLevelExists(this.currentLevel + 1)) {
            this.currentLevel++;
            this.reloadCurrentLevel();
        }
    }

    public loadPreviousLevel(): void {
        if (this.currentLevel > 0) {
            this.currentLevel--;
            this.reloadCurrentLevel();
        }
    }

    public reloadCurrentLevel(): void {
        this.node.removeAllChildren();
        this.loadLevel();
    }

    // Level validation methods
    public isLevelExists(levelIndex: number): boolean {
        return this.levelData?.json && Array.isArray(this.levelData.json) &&
               levelIndex >= 0 && levelIndex < this.levelData.json.length;
    }

    public getAvailableLevelsCount(): number {
        return this.levelData?.json?.length || 0;
    }

    public getAvailableLevelIndices(): number[] {
        return Array.from({ length: this.getAvailableLevelsCount() }, (_, i) => i);
    }

    /**
     * Lấy tile component theo layer và position
     */
    public getTileAt(layerIndex: number, x: number, y: number): Tile | null {
        const tileInfo = this.allTileComponents.find(tile =>
            tile.layerIndex === layerIndex &&
            tile.tileData.x === x &&
            tile.tileData.y === y &&
            tile.tileData.isDisplay
        );

        return tileInfo ? tileInfo.component : null;
    }

    private loadLevel() {
        if (!this.validateLevelData()) return;

        const levelData = this.levelData.json[this.currentLevel];
        const tileMap = levelData.tilemap;

        this.allTileComponents = [];

        // Process layers (reverse order for proper z-index)
        for (let layerIndex = tileMap.length - 1; layerIndex >= 0; layerIndex--) {
            this.createLayer(tileMap[layerIndex], layerIndex, tileMap.length);
        }

        // Process blocking logic after layout
        this.scheduleOnce(() => {
            this.processBlockingLogic(this.allTileComponents);
        }, 0.1);

        GameManager.Instance.setGameState(GameState.Play);
    }

    private validateLevelData(): boolean {
        if (!this.levelData?.json || !Array.isArray(this.levelData.json)) {
            console.error("MapLoader: Invalid level data");
            return false;
        }

        if (!this.isLevelExists(this.currentLevel)) {
            console.error(`MapLoader: Level ${this.currentLevel} out of range`);
            return false;
        }

        const levelData = this.levelData.json[this.currentLevel];
        if (!levelData?.tilemap || !Array.isArray(levelData.tilemap)) {
            console.error("MapLoader: Invalid tilemap data");
            return false;
        }

        return true;
    }

    private createLayer(layerData: any, layerIndex: number, totalLayers: number): void {
        if (!layerData?.tiles || !Array.isArray(layerData.tiles)) {
            console.warn(`MapLoader: Invalid layer ${layerIndex}`);
            return;
        }

        const layerNode = this.createLayerNode(layerIndex);
        layerNode.setParent(this.node);
        layerNode.setSiblingIndex(totalLayers - 1 - layerIndex);
        layerNode.active = layerData.isDisplay;

        const gridInfo = this.calculateGridCenter(layerData.tiles);

        // Create tiles
        layerData.tiles.forEach((tile: any) => {
            if (tile?.isDisplay) {
                this.createAndSetupTile(tile, layerNode, layerIndex, gridInfo);
            }
        });

        layerNode.setPosition(Vec3.ZERO);
    }

    private createAndSetupTile(tileData: any, layerNode: Node, layerIndex: number, gridInfo: any): void {
        const tileNode = this.createTile(tileData, gridInfo);
        tileNode.setParent(layerNode);

        const tileComponent = tileNode.getComponent(Tile);
        if (tileComponent) {
            tileComponent.setupView(tileData);
            this.allTileComponents.push({
                component: tileComponent,
                layerIndex: layerIndex,
                tileData: tileData
            });
        }
    }

    /**
     * Tạo layer node từ prefab
     */
    private createLayerNode(layerIndex: number): Node {
        let layerNode: Node;

        if (this.layerPrefab) {
            // Tạo từ prefab nếu có
            layerNode = instantiate(this.layerPrefab);
        } else {
            // Tạo node trống nếu không có prefab
            layerNode = new Node();
        }

        // Đặt tên layer
        layerNode.name = `Layer${layerIndex}`;

        return layerNode;
    }

    private createTile(tileData: any, gridInfo: any): Node {
        const tileNode = instantiate(this.tilePrefab);

        const posX = (tileData.x * MapLoader.TILE_SIZE) - gridInfo.offsetX;
        const posY = -(tileData.y * MapLoader.TILE_SIZE) + gridInfo.offsetY;

        tileNode.setPosition(posX, posY, 0);
        return tileNode;
    }

    private calculateGridCenter(layer: any[]): { offsetX: number, offsetY: number } {
        if (!layer?.length) return { offsetX: 0, offsetY: 0 };

        const coords = layer.filter(tile => tile).map(tile => ({ x: tile.x, y: tile.y }));
        const minX = Math.min(...coords.map(c => c.x));
        const maxX = Math.max(...coords.map(c => c.x));
        const minY = Math.min(...coords.map(c => c.y));
        const maxY = Math.max(...coords.map(c => c.y));

        const gridWidth = (maxX - minX + 1) * MapLoader.TILE_SIZE;
        const gridHeight = (maxY - minY + 1) * MapLoader.TILE_SIZE;

        return {
            offsetX: gridWidth / 2 - MapLoader.TILE_SIZE / 2,
            offsetY: gridHeight / 2 - MapLoader.TILE_SIZE / 2
        };
    }
    /**
     * Xử lý logic blocking - tile ở layer dưới bị che bởi tile ở layer trên
     */
    private processBlockingLogic(allTileComponents: { component: Tile, layerIndex: number, tileData: any }[]): void {
        allTileComponents.sort((a, b) => a.layerIndex - b.layerIndex);

        allTileComponents.forEach(currentTile => {
            // Block tiles with isDisplay = false
            if (!currentTile.tileData.isDisplay) {
                currentTile.component.isBlocked = true;
                return;
            }

            // Skip layer 0 (top layer)
            if (currentTile.layerIndex < 1) return;

            currentTile.component.clearBlockingParents();

            // Check blocking with upper layers
            allTileComponents
                .filter(upperTile =>
                    upperTile.layerIndex < currentTile.layerIndex &&
                    upperTile.tileData.isDisplay
                )
                .forEach(upperTile => {
                    if (this.isTileOverlapping(currentTile.component.node, upperTile.component.node)) {
                        currentTile.component.addBlockingParent(upperTile.component);
                        upperTile.component.addBlockingChild(currentTile.component);
                    }
                });

            // Set blocked status
            if (currentTile.component.hasBlockingParents) {
                currentTile.component.isBlocked = true;
            }
        });
    }

    /**
     * Kiểm tra overlap giữa hai tile nodes sử dụng Intersection2D
     */
    private isTileOverlapping(node1: Node, node2: Node): boolean {
        const transform1 = node1.getComponent(UITransform);
        const transform2 = node2.getComponent(UITransform);

        if (!transform1 || !transform2) {
            return false;
        }

        // Tạo Rect cho mỗi tile dựa trên world position và size
        const worldPos1 = node1.getWorldPosition();
        const worldPos2 = node2.getWorldPosition();

        const rect1 = new Rect(
            worldPos1.x - transform1.width / 2,
            worldPos1.y - transform1.height / 2,
            transform1.width,
            transform1.height
        );

        const rect2 = new Rect(
            worldPos2.x - transform2.width / 2,
            worldPos2.y - transform2.height / 2,
            transform2.width,
            transform2.height
        );

        // Sử dụng Intersection2D để kiểm tra overlap
        return Intersection2D.rectRect(rect1, rect2);
    }

}


