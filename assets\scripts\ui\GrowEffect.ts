import { _decorator, Component, Node, Sprite, tween, UIOpacity } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GrowEffect')
export class GrowEffect extends Component {

    @property(UIOpacity) uiOpacity: UIOpacity = null!;

    start() {
        this.playAnimation();
    }

    public playAnimation() {
        

        

        tween(this.uiOpacity).stop();

        tween(this.uiOpacity)
            // 1. Trong 0.5 giây, mờ đi đến opacity 200
            .to(0.5, { opacity: 150 })
            // 2. <PERSON>u đ<PERSON>, trong 0.5 giây, quay trở lại opacity 255
            .to(0.5, { opacity: 255 })
            // 3. Lặp lại chuỗi hành động tuần tự này mãi mãi
            .union()
            .repeatForever()
            .start();
    }


}


