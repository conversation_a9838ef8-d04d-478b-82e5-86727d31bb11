import { _decorator, Component, Node } from 'cc';
import { SpawnTile } from './SpawnTile';
import { TileMahjong } from './TileMahjong';
const { ccclass, property } = _decorator;

@ccclass('GameControl')
export class GameControl extends Component {

    public static instance: GameControl = null!;

    @property(SpawnTile) private spawnTile: SpawnTile = null!;

    public arrTile: TileMahjong[] = [];

    // Tutorial properties
    private tutorialTimer: number = 0;
    private isTutorialActive: boolean = false;
    private tutorialInterval: number = 3; // 3 giây

    protected onLoad(): void {
        GameControl.instance = this;
    }

    protected start(): void {
        this.spawnTile.startSpawn();
    }

    protected update(deltaTime: number): void {
        this.updateTutorial(deltaTime);
    }

    public stopSpawn() {
        this.spawnTile.stopSpawn();
    }

    /**
     * Cập nhật logic tutorial
     */
    private updateTutorial(deltaTime: number): void {
        // Kiểm tra điều kiện bắt đầu tutorial
        if (this.arrTile.length >= 2 && !this.isTutorialActive) {
            this.startTutorial();
        }

        // Cập nhật timer nếu tutorial đang hoạt động
        if (this.isTutorialActive) {
            this.tutorialTimer += deltaTime;

            // Mỗi 3 giây hiển thị tutorial cho tile random
            if (this.tutorialTimer >= this.tutorialInterval) {
                this.showRandomTileTutorial();
                this.tutorialTimer = 0; // Reset timer
            }
        }
    }

    /**
     * Bắt đầu tutorial - hiển thị tutorial cho tile đầu tiên
     */
    private startTutorial(): void {
        if (this.arrTile.length === 0) return;

        console.log("GameControl: Starting tutorial with first tile");

        // Hiển thị tutorial cho tile đầu tiên
        const firstTile = this.arrTile[0];
        if (firstTile) {
            firstTile.showTutorial();
            console.log("GameControl: Showed tutorial for first tile");
        }

        // Bắt đầu tutorial timer
        this.isTutorialActive = true;
        this.tutorialTimer = 0;
    }

    /**
     * Hiển thị tutorial cho tile ngẫu nhiên
     */
    private showRandomTileTutorial(): void {
        if (this.arrTile.length === 0) return;

        // Lấy tile ngẫu nhiên từ mảng
        const randomIndex = Math.floor(Math.random() * this.arrTile.length);
        const randomTile = this.arrTile[randomIndex];

        if (randomTile) {
            randomTile.showTutorial();
            console.log(`GameControl: Showed tutorial for random tile at index ${randomIndex}`);
        }
    }

    /**
     * Dừng tutorial (có thể gọi khi cần thiết)
     */
    public stopTutorial(): void {
        this.isTutorialActive = false;
        this.tutorialTimer = 0;
        console.log("GameControl: Tutorial stopped");
    }

    /**
     * Thêm tile vào mảng (gọi từ bên ngoài khi có tile mới)
     */
    public addTileToArray(tile: TileMahjong): void {
        this.arrTile.push(tile);
        console.log(`GameControl: Added tile to array. Total tiles: ${this.arrTile.length}`);
    }

    /**
     * Xóa tile khỏi mảng (gọi khi tile bị destroy hoặc không còn cần thiết)
     */
    public removeTileFromArray(tile: TileMahjong): void {
        const index = this.arrTile.indexOf(tile);
        if (index > -1) {
            this.arrTile.splice(index, 1);
            console.log(`GameControl: Removed tile from array. Total tiles: ${this.arrTile.length}`);

            // Nếu không còn đủ tile thì dừng tutorial
            if (this.arrTile.length < 2) {
                this.stopTutorial();
            }
        }
    }
}


