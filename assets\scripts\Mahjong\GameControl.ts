import { _decorator, Component, Node } from 'cc';
import { SpawnTile } from './SpawnTile';
import { TileMahjong } from './TileMahjong';
import { Tray<PERSON>ajong } from './TrayMajong';
const { ccclass, property } = _decorator;

@ccclass('GameControl')
export class GameControl extends Component {

    public static instance: GameControl = null!;

    @property(SpawnTile) private spawnTile: SpawnTile = null!;
    @property(TrayMajong) private trayMajong: TrayMajong = null!;

    public arrTile: TileMahjong[] = [];

    // Tutorial properties
    private tutorialTimer: number = 0;
    private isTutorialActive: boolean = false;
    private tutorialInterval: number = 3; // 3 giây

    protected onLoad(): void {
        GameControl.instance = this;

        TileMahjong.onTileTap.on(this.resetTutorial, this);
    }

    protected start(): void {
        this.spawnTile.startSpawn();
    }

    protected update(deltaTime: number): void {
        this.updateTutorial(deltaTime);
    }

    public stopSpawn() {
        this.spawnTile.stopSpawn();
    }

    protected onDestroy(): void {
        TileMahjong.onTileTap.off(this.resetTutorial);
    }

    private resetTutorial() {
        this.tutorialTimer = 0; // Reset timer về 0 để chờ đúng 5s theo tutorialInterval
    }

    /**
     * Cập nhật logic tutorial
     */
    private updateTutorial(deltaTime: number): void {
        // Lấy số lượng tile không nằm trên tray
        const availableTilesCount = this.getAvailableTilesCount();

        // Kiểm tra điều kiện bắt đầu tutorial
        if (availableTilesCount >= 2 && !this.isTutorialActive) {
            this.startTutorial();
        }

        // Cập nhật timer nếu tutorial đang hoạt động
        if (this.isTutorialActive) {
            this.tutorialTimer += deltaTime;

            // Mỗi 3 giây hiển thị tutorial cho tile mới nhất
            if (this.tutorialTimer >= this.tutorialInterval) {
                this.showRandomTileTutorial();
                this.tutorialTimer = 0; // Reset timer
            }
        }

        // Dừng tutorial nếu không còn đủ tile khả dụng
        if (availableTilesCount < 2 && this.isTutorialActive) {
            this.stopTutorial();
        }
    }

    /**
     * Lấy số lượng tile không nằm trên tray
     */
    private getAvailableTilesCount(): number {
        return this.arrTile.filter(tile => !tile.isInTray).length;
    }

    /**
     * Lấy tile đầu tiên không nằm trên tray
     */
    private getFirstAvailableTile(): TileMahjong | null {
        return this.arrTile.find(tile => !tile.isInTray) || null;
    }

    /**
     * Lấy tile mới nhất không nằm trên tray
     */
    private getLatestAvailableTile(): TileMahjong | null {
        // Duyệt từ cuối mảng về đầu để tìm tile mới nhất không ở tray
        for (let i = this.arrTile.length - 1; i >= 0; i--) {
            if (!this.arrTile[i].isInTray) {
                return this.arrTile[i];
            }
        }
        return null;
    }

    /**
     * Bắt đầu tutorial - hiển thị tutorial cho tile đầu tiên không ở tray
     */
    private startTutorial(): void {
        const firstAvailableTile = this.getFirstAvailableTile();
        if (!firstAvailableTile) return;

        console.log("GameControl: Starting tutorial with first available tile");

        firstAvailableTile.showTutorial();
        console.log("GameControl: Showed tutorial for first available tile");

        // Bắt đầu tutorial timer
        this.isTutorialActive = true;
        this.tutorialTimer = 0;
    }

    /**
     * Hiển thị tutorial cho tile mới tạo gần nhất không ở tray
     */
    private showRandomTileTutorial(): void {
        const latestAvailableTile = this.getLatestAvailableTile();

        if (latestAvailableTile) {
            latestAvailableTile.showTutorial();
            const tileIndex = this.arrTile.indexOf(latestAvailableTile);
            console.log(`GameControl: Showed tutorial for latest available tile at index ${tileIndex}`);
        } else {
            console.log("GameControl: No available tiles for tutorial (all tiles are in tray)");
        }
    }

    /**
     * Dừng tutorial (có thể gọi khi cần thiết)
     */
    public stopTutorial(): void {
        this.isTutorialActive = false;
        this.tutorialTimer = 0;
        console.log("GameControl: Tutorial stopped");
    }

    /**
     * Thêm tile vào mảng (gọi từ bên ngoài khi có tile mới)
     */
    public addTileToArray(tile: TileMahjong): void {
        this.arrTile.push(tile);
        console.log(`GameControl: Added tile to array. Total tiles: ${this.arrTile.length}`);
    }

    /**
     * Xóa tile khỏi mảng (gọi khi tile bị destroy hoặc không còn cần thiết)
     */
    public removeTileFromArray(tile: TileMahjong): void {
        const index = this.arrTile.indexOf(tile);
        if (index > -1) {
            this.arrTile.splice(index, 1);
            console.log(`GameControl: Removed tile from array. Total tiles: ${this.arrTile.length}`);

            // Kiểm tra số lượng tile khả dụng (không ở tray)
            const availableTilesCount = this.getAvailableTilesCount();
            if (availableTilesCount < 2) {
                this.stopTutorial();
            }
        }
    }
}


