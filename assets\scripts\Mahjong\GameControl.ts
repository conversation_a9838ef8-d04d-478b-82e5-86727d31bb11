import { _decorator, Component, Node, sys } from 'cc';
import { SpawnTile } from './SpawnTile';
import { TileMahjong } from './TileMahjong';
import { TrayMajong } from './TrayMajong';
import { SoundAdapter } from './SoundAdapter';
import { CountTileMatch } from './CountTileMatch';
const { ccclass, property } = _decorator;

@ccclass('GameControl')
export class GameControl extends Component {

    public static instance: GameControl = null!;

    @property(SpawnTile) private spawnTile: SpawnTile = null!;
    @property(TrayMajong) private trayMajong: TrayMajong = null!;
    @property(CountTileMatch) private countTileMatch: CountTileMatch = null!;
    @property(Node) private touchZone: Node = null!;

    public arrTile: TileMahjong[] = [];

    // Tutorial properties
    private tutorialTimer: number = 0;
    private isTutorialActive: boolean = false;
    private tutorialInterval: number = 5; // 3 giây

    protected onLoad(): void {
        GameControl.instance = this;

        TileMahjong.onTileTap.on(this.resetTutorial, this);
        this.trayMajong.onStartCheckMatch.on(this.countTileMatch.match3, this);
    }

    protected start(): void {
        SoundAdapter.instance.playMusic();
        this.spawnTile.startSpawn();
    }

    protected update(deltaTime: number): void {
        this.updateTutorial(deltaTime);
    }

    public stopSpawn() {
        this.spawnTile.stopSpawn();
    }

    protected onDestroy(): void {
        TileMahjong.onTileTap.off(this.resetTutorial);
    }

    private resetTutorial() {
        this.tutorialTimer = 0; // Reset timer về 0 để chờ đúng 5s theo tutorialInterval

        const canShow = this.countTileMatch.checkCount();

        if (canShow) {
            this.cta();
            this.touchZone.active = true;
            this.spawnTile.stopSpawn();
        }
    }

    /**
     * Cập nhật logic tutorial
     */
    private updateTutorial(deltaTime: number): void {
        // Lấy số lượng tile không nằm trên tray
        const availableTilesCount = this.getAvailableTilesCount();

        // Kiểm tra điều kiện bắt đầu tutorial
        if (availableTilesCount >= 2 && !this.isTutorialActive) {
            this.startTutorial();
        }

        // Cập nhật timer nếu tutorial đang hoạt động
        if (this.isTutorialActive) {
            this.tutorialTimer += deltaTime;

            // Mỗi 3 giây hiển thị tutorial cho tile mới nhất
            if (this.tutorialTimer >= this.tutorialInterval) {
                this.showRandomTileTutorial();
                this.tutorialTimer = 0; // Reset timer
            }
        }

        // Dừng tutorial nếu không còn đủ tile khả dụng
        if (availableTilesCount < 2 && this.isTutorialActive) {
            this.stopTutorial();
        }
    }

    /**
     * Lấy số lượng tile không nằm trên tray
     */
    private getAvailableTilesCount(): number {
        return this.arrTile.filter(tile => !tile.isInTray).length;
    }

    /**
     * Lấy tile đầu tiên không nằm trên tray
     */
    private getFirstAvailableTile(): TileMahjong | null {
        return this.arrTile.find(tile => !tile.isInTray) || null;
    }

    /**
     * Lấy tile mới nhất không nằm trên tray
     */
    private getLatestAvailableTile(): TileMahjong | null {
        // Duyệt từ cuối mảng về đầu để tìm tile mới nhất không ở tray
        for (let i = this.arrTile.length - 1; i >= 0; i--) {
            if (!this.arrTile[i].isInTray) {
                return this.arrTile[i];
            }
        }
        return null;
    }

    /**
     * Lấy loại tile có nhiều nhất trên tray
     */
    private getMostFrequentTileTypeInTray(): number | null {
        if (!this.trayMajong || this.trayMajong.isEmpty) {
            return null;
        }

        const tileTypeCount = this.trayMajong.getTileTypeCount();
        let maxCount = 0;
        let mostFrequentType: number | null = null;

        for (const typeStr in tileTypeCount) {
            const type = parseInt(typeStr);
            const count = tileTypeCount[type];
            if (count > maxCount) {
                maxCount = count;
                mostFrequentType = type;
            }
        }

        return mostFrequentType;
    }

    /**
     * Lấy tile ưu tiên để hiển thị tutorial
     * Ưu tiên: tile cùng loại với tile có nhiều nhất trên tray > tile gần nhất > tile mới nhất
     */
    private getPriorityTileForTutorial(): TileMahjong | null {
        const availableTiles = this.arrTile.filter(tile => !tile.isInTray);
        if (availableTiles.length === 0) {
            return null;
        }

        // Nếu tray có tile, ưu tiên tile cùng loại với tile có nhiều nhất trên tray
        const mostFrequentType = this.getMostFrequentTileTypeInTray();
        if (mostFrequentType !== null) {
            // Tìm tile mới nhất cùng loại với tile có nhiều nhất trên tray
            for (let i = availableTiles.length - 1; i >= 0; i--) {
                if (availableTiles[i].tileType === mostFrequentType) {
                    return availableTiles[i];
                }
            }

            // Nếu không có tile cùng loại, tìm tile gần nhất với tray
            const closestTile = this.getClosestTileToTray(availableTiles);
            if (closestTile) {
                return closestTile;
            }
        }

        // Nếu không tìm thấy tile cùng loại hoặc tile gần nhất, trả về tile mới nhất
        return availableTiles[availableTiles.length - 1];
    }

    /**
     * Tìm tile gần nhất với tray (theo khoảng cách vị trí)
     */
    private getClosestTileToTray(availableTiles: TileMahjong[]): TileMahjong | null {
        if (!this.trayMajong || availableTiles.length === 0) {
            return null;
        }

        const trayWorldPos = this.trayMajong.node.getWorldPosition();
        let closestTile: TileMahjong | null = null;
        let minDistance = Infinity;

        for (const tile of availableTiles) {
            const tileWorldPos = tile.node.getWorldPosition();
            const distance = tileWorldPos.subtract(trayWorldPos).length();

            if (distance < minDistance) {
                minDistance = distance;
                closestTile = tile;
            }
        }

        return closestTile;
    }

    /**
     * Bắt đầu tutorial - hiển thị tutorial cho tile đầu tiên không ở tray
     */
    private startTutorial(): void {
        const firstAvailableTile = this.getFirstAvailableTile();
        if (!firstAvailableTile) return;

        console.log("GameControl: Starting tutorial with first available tile");

        firstAvailableTile.showTutorial();
        console.log("GameControl: Showed tutorial for first available tile");

        // Bắt đầu tutorial timer
        this.isTutorialActive = true;
        this.tutorialTimer = 0;
    }

    /**
     * Hiển thị tutorial cho tile ưu tiên
     * Ưu tiên: tile cùng loại > tile gần nhất > tile mới nhất
     */
    private showRandomTileTutorial(): void {
        const priorityTile = this.getPriorityTileForTutorial();

        if (priorityTile) {
            priorityTile.showTutorial();
            const tileIndex = this.arrTile.indexOf(priorityTile);
            const mostFrequentType = this.getMostFrequentTileTypeInTray();

            // Xác định lý do chọn tile này
            if (mostFrequentType !== null && priorityTile.tileType === mostFrequentType) {
                console.log(`GameControl: Showed tutorial for PRIORITY tile (type ${priorityTile.tileType}) matching most frequent tray type at index ${tileIndex}`);
            } else if (mostFrequentType !== null) {
                // Kiểm tra xem có phải tile gần nhất không
                const availableTiles = this.arrTile.filter(tile => !tile.isInTray);
                const closestTile = this.getClosestTileToTray(availableTiles);
                if (closestTile === priorityTile) {
                    console.log(`GameControl: Showed tutorial for CLOSEST tile (type ${priorityTile.tileType}) to tray at index ${tileIndex}`);
                } else {
                    console.log(`GameControl: Showed tutorial for LATEST tile (type ${priorityTile.tileType}) at index ${tileIndex}`);
                }
            } else {
                console.log(`GameControl: Showed tutorial for LATEST tile (type ${priorityTile.tileType}) - tray empty at index ${tileIndex}`);
            }
        } else {
            console.log("GameControl: No available tiles for tutorial (all tiles are in tray)");
        }
    }

    /**
     * Dừng tutorial (có thể gọi khi cần thiết)
     */
    public stopTutorial(): void {
        this.isTutorialActive = false;
        this.tutorialTimer = 0;
        console.log("GameControl: Tutorial stopped");
    }

    /**
     * Thêm tile vào mảng (gọi từ bên ngoài khi có tile mới)
     */
    public addTileToArray(tile: TileMahjong): void {
        this.arrTile.push(tile);
        console.log(`GameControl: Added tile to array. Total tiles: ${this.arrTile.length}`);
    }

    /**
     * Xóa tile khỏi mảng (gọi khi tile bị destroy hoặc không còn cần thiết)
     */
    public removeTileFromArray(tile: TileMahjong): void {
        const index = this.arrTile.indexOf(tile);
        if (index > -1) {
            this.arrTile.splice(index, 1);
            console.log(`GameControl: Removed tile from array. Total tiles: ${this.arrTile.length}`);

            // Kiểm tra số lượng tile khả dụng (không ở tray)
            const availableTilesCount = this.getAvailableTilesCount();
            if (availableTilesCount < 2) {
                this.stopTutorial();
            }
        }
    }

    cta() {


        const ad_network = window['advChannels'];
        if (!!ad_network) {
            switch (ad_network) {
                case "Mintegral": {
                    window['install'] && window['install']();
                    return;
                }
                case "Unity":
                case "AppLovin": {
                    let open;
                    if (sys.os == sys.OS.ANDROID) {
                        open = window["mraidOpenPlayStore"];
                    } else {
                        open = window["mraidOpenAppStore"];
                    }
                    open?.();
                    return;
                };
            }
        }
        sys.openURL("https://play.google.com/store/apps/details?id=com.legendarylabs.triple.mahjong.royal.victorian");
    }
}


