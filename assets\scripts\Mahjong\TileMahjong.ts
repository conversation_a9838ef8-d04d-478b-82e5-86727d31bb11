import { _decorator, Component, Node, Sprite, Vec3, tween, UITransform, Color, SpriteFrame, AnimationComponent } from 'cc';
import { Signal } from '../eventSystem/Signal';
import { BubbleAnimation } from './BubbleAnimation';
const { ccclass, property } = _decorator;

@ccclass('TileMahjong')
export class TileMahjong extends Component {

    @property(Sprite) private tileSprite: Sprite = null!;
    @property(BubbleAnimation) private bubbleAnimation: BubbleAnimation = null!;
    @property(Node) private visual: Node = null!;
    @property(SpriteFrame) private tileSpriteFrames: SpriteFrame[] = [];
    @property(Node) private tutorialNode: Node = null!;

    @property(AnimationComponent) private anim;

    private _tileType: number = 0;
    private _isSelected: boolean = false;
    private _isInTray: boolean = false;
    private _isMoving: boolean = false;
    private _originalPosition: Vec3 = new Vec3();
    private _currentTweens: any[] = []; // Lưu trữ các tween đang chạy
    private _shouldStopFloat: boolean = false; // Flag để dừng float animation

    // Properties cho startMove animation
    private _moveStartTime: number = 0;
    private _moveDuration: number = 0;
    private _moveStartPos: Vec3 = new Vec3();
    private _moveTargetPos: Vec3 = new Vec3();
    private _moveAmplitude: number = 0;
    private _moveFrequency: number = 0;
    private _isUsingUpdate: boolean = false;
    private _moveResolve: (() => void) | null = null;

    public static onTileTap: Signal<TileMahjong> = new Signal();

    // Getters and Setters
    get tileType(): number { return this._tileType; }
    set tileType(value: number) { this._tileType = value; this.tileSprite.spriteFrame = this.tileSpriteFrames[value]; }

    get isSelected(): boolean { return this._isSelected; }
    get isInTray(): boolean { return this._isInTray; }
    set isInTray(value: boolean) {
        this._isInTray = value;
        this.updateTrayState();
    }

    get isMoving(): boolean { return this._isMoving; }

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_START, this.onTap, this);
        this._originalPosition = this.node.position.clone();
    }

    protected onDestroy(): void {
        this.node.off(Node.EventType.TOUCH_START, this.onTap, this);
    }

    protected update(deltaTime: number): void {
        if (this._isUsingUpdate && this._isMoving) {
            this.updateMoveAnimation(deltaTime);
        }
    }

    private onTap(): void {
        if (this._isInTray) return; // Không cho phép tap khi đã ở trong tray

        if (this._isMoving) {
            // Nếu đang di chuyển, dừng tất cả animation
            this.stopAllMovement();
        }

        this.bubbleAnimation.playAnimation(this.flyon.bind(this));
    }

    private flyon() {
        TileMahjong.onTileTap?.trigger(this);
    }

    public showTutorial() {
        this.tutorialNode.active = true;

        this.anim.once('finished', () => {
            this.tutorialNode.active = false;
        });
    }

    /**
     * Dừng tất cả animation di chuyển
     */
    private stopAllMovement(): void {
        // Set flag để dừng float animations
        this._shouldStopFloat = true;

        // Dừng update animation
        this._isUsingUpdate = false;

        // Resolve move promise nếu đang chạy
        if (this._moveResolve) {
            this._moveResolve();
            this._moveResolve = null;
        }

        // Dừng tất cả tween đang chạy
        this._currentTweens.forEach(tween => {
            if (tween && tween.stop) {
                tween.stop();
            }
        });
        this._currentTweens = [];

        // Dừng tất cả tween trên node bằng cách clear tween system
        this.unscheduleAllCallbacks(); // Dừng tất cả schedule callbacks

        // Reset trạng thái
        this._isMoving = false;
        this.node.angle = 0; // Reset rotation

        console.log("TileMahjong: All movement stopped by tap");
    }

    /**
     * Xóa tween khỏi mảng theo dõi
     */
    private clearTweenFromArray(tween: any): void {
        const index = this._currentTweens.indexOf(tween);
        if (index > -1) {
            this._currentTweens.splice(index, 1);
        }
    }

    /**
     * Đánh dấu tile được chọn
     */
    setSelected(selected: boolean) {
        this._isSelected = selected;
        if (selected) {
            this.playSelectAnimation();
        }
    }

    /**
     * Animation khi tile được chọn
     */
    private playSelectAnimation() {
        const originalScale = this.visual.scale.clone();
        tween(this.visual)
            .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
            .to(0.1, { scale: originalScale })
            .start();
    }



    /**
     * Animation di chuyển tile đến vị trí mới (sử dụng world position)
     */
    moveTo(targetWorldPosition: Vec3, duration: number = 0.3): Promise<void> {
        return new Promise((resolve) => {
            const parent = this.node.parent;
            if (!parent) {
                console.warn("TileMahjong node has no parent, cannot convert world position");
                resolve();
                return;
            }

            const parentUITransform = parent.getComponent(UITransform);
            if (!parentUITransform) {
                console.warn("Parent node has no UITransform component");
                resolve();
                return;
            }

            // Đánh dấu đang di chuyển
            this._isMoving = true;

            // Chuyển đổi từ world position sang local position của parent
            const localPosition = parentUITransform.convertToNodeSpaceAR(targetWorldPosition);

            const positionTween = tween(this.node)
                .to(duration, {
                    position: localPosition,
                    // scale: new Vec3(0.8, 0.8, 1)
                })
                .call(() => {
                    this._isMoving = false; // Kết thúc di chuyển
                    this.clearTweenFromArray(positionTween);
                    this.clearTweenFromArray(scaleTween);
                    resolve();
                })
                .start();

            const scaleTween = tween(this.visual)
                .to(duration, {
                    scale: new Vec3(0.7, 0.7, 1)
                })
                .start();

            // Lưu trữ tween references
            this._currentTweens.push(positionTween);
            this._currentTweens.push(scaleTween);
        });
    }

    /**
     * Animation biến mất (khi match thành công)
     */
    playDisappearAnimation(delay: number = 0): Promise<void> {
        return new Promise((resolve) => {
            tween(this.visual)
                .delay(delay)
                .to(0.2, { scale: new Vec3(1, 1, 1) })
                .to(0.2, { scale: new Vec3(0, 0, 0) })
                .call(() => {
                    resolve();
                })
                .start();
        });
    }

    /**
     * Reset tile về trạng thái ban đầu
     */
    resetTile() {
        // Dừng tất cả animation trước khi reset
        this.stopAllMovement();

        this._tileType = 0;
        this._isSelected = false;
        this._isInTray = false;
        this._isMoving = false;
        this._shouldStopFloat = false;
        this._isUsingUpdate = false;
        this._moveResolve = null;
        this.node.scale = Vec3.ONE.clone();
        this.node.position = this._originalPosition.clone();
        this.node.angle = 0; // Reset rotation

        if (this.tileSprite) this.tileSprite.color = Color.WHITE;
    }

    /**
     * Cập nhật trạng thái khi ở trong tray
     */
    private updateTrayState() {
        if (this._isInTray) {
            this.visual.scale = new Vec3(0.7, 0.7, 1);
            if (this.tileSprite) this.tileSprite.color = Color.WHITE;
        } else {
            this.node.scale = Vec3.ONE.clone();
        }
    }

    /**
     * Di chuyển tile theo đường uốn lượn dọc như bong bóng nổi lên mặt nước
     * Sử dụng hàm update để animation mượt mà
     * @param pos - World position đích
     * @param duration - Thời gian di chuyển (mặc định 3.5s - chậm hơn)
     * @param amplitude - Độ lớn của sóng uốn lượn dọc (mặc định 25 - nhẹ hơn)
     * @param frequency - Tần số sóng (mặc định 1.0 - chậm hơn)
     */
    public startMove(pos: Vec3, duration: number = 10, amplitude: number = 25, frequency: number = 1.0): Promise<void> {
        return new Promise((resolve) => {
            const parent = this.node.parent;
            if (!parent) {
                console.warn("TileMahjong node has no parent, cannot convert world position");
                resolve();
                return;
            }

            const parentUITransform = parent.getComponent(UITransform);
            if (!parentUITransform) {
                console.warn("Parent node has no UITransform component");
                resolve();
                return;
            }

            // Đánh dấu đang di chuyển
            this._isMoving = true;
            this._isUsingUpdate = true;

            // Chuyển đổi world position sang local position
            this._moveTargetPos = parentUITransform.convertToNodeSpaceAR(pos);
            this._moveStartPos = this.node.position.clone();
            this._moveStartTime = 0;
            this._moveDuration = duration;
            this._moveAmplitude = amplitude;
            this._moveFrequency = frequency;

            // Lưu resolve function để gọi khi hoàn thành
            this._moveResolve = resolve;

            console.log("TileMahjong: startMove animation started");
        });
    }

    /**
     * Cập nhật animation di chuyển trong hàm update
     */
    private updateMoveAnimation(deltaTime: number): void {
        this._moveStartTime += deltaTime;

        // Kiểm tra xem đã hoàn thành chưa
        if (this._moveStartTime >= this._moveDuration) {
            // Đảm bảo tile đến đúng vị trí cuối
            this.node.position = this._moveTargetPos;
            this.node.angle = 0; // Reset rotation
            this._isMoving = false;
            this._isUsingUpdate = false;

            if (this._moveResolve) {
                this._moveResolve();
                this._moveResolve = null;
            }
            return;
        }

        // Tính toán progress (0 -> 1)
        const progress = this._moveStartTime / this._moveDuration;

        // Easing function cho chuyển động mượt mà và chậm (ease-in-out)
        const easedProgress = progress < 0.5
            ? 2 * progress * progress
            : 1 - Math.pow(-2 * progress + 2, 2) / 2;

        // Vị trí cơ bản theo đường thẳng
        const deltaX = this._moveTargetPos.x - this._moveStartPos.x;
        const deltaY = this._moveTargetPos.y - this._moveStartPos.y;

        const baseX = this._moveStartPos.x + deltaX * easedProgress;
        const baseY = this._moveStartPos.y + deltaY * easedProgress;

        // Hiệu ứng sóng uốn lượn theo chiều dọc (vertical wave)
        // Sóng dọc chính - tạo hiệu ứng bong bóng nổi lên
        const verticalWave = Math.sin(progress * Math.PI * this._moveFrequency) * this._moveAmplitude * (1 - progress);

        // Sóng dọc phụ - tạo hiệu ứng dao động nhẹ
        const verticalBobbing = Math.sin(progress * Math.PI * this._moveFrequency * 2) * (this._moveAmplitude * 0.2) * (1 - progress);

        // Hiệu ứng floating lên (như bong bóng nổi)
        const floatEffect = Math.sin(progress * Math.PI * 0.8) * (this._moveAmplitude * 0.3);

        // Tính toán vị trí cuối cùng - chỉ thêm hiệu ứng vào trục Y
        const finalX = baseX; // Không thêm hiệu ứng ngang
        const finalY = baseY + verticalWave + verticalBobbing + floatEffect;

        this.node.setPosition(finalX, finalY, this._moveStartPos.z);

        // Thêm hiệu ứng xoay rất nhẹ theo sóng dọc
        this.node.angle = Math.sin(progress * Math.PI * this._moveFrequency) * 4 * (1 - progress);
    }

}


