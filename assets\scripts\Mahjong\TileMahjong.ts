import { _decorator, Component, Node, Sprite } from 'cc';
import { Signal } from '../eventSystem/Signal';
import { BubbleAnimation } from './BubbleAnimation';
const { ccclass, property } = _decorator;

@ccclass('TileMahjong')
export class TileMahjong extends Component {

    @property(Sprite) private tileSprite: Sprite = null!;
    @property(BubbleAnimation) private bubbleAnimation: BubbleAnimation = null!;

    public static onTileTap: Signal<TileMahjong> = new Signal();

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_START, this.onTap, this);
    }

    protected onDestroy(): void {
        this.node.off(Node.EventType.TOUCH_START, this.onTap, this);
    }

    private onTap(): void {
        this.bubbleAnimation.playAnimation(this.flyon.bind(this));
    }

    private flyon() {
        TileMahjong.onTileTap?.trigger(this);
    }

}


