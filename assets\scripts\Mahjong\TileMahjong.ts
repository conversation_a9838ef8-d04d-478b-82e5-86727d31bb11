import { _decorator, Component, Node, Sprite, Vec3, tween, UITransform, Color } from 'cc';
import { Signal } from '../eventSystem/Signal';
import { BubbleAnimation } from './BubbleAnimation';
const { ccclass, property } = _decorator;

@ccclass('TileMahjong')
export class TileMah<PERSON> extends Component {

    @property(Sprite) private tileSprite: Sprite = null!;
    @property(BubbleAnimation) private bubbleAnimation: BubbleAnimation = null!;
    @property(Node) private visual: Node = null!;

    private _tileType: number = 0;
    private _isSelected: boolean = false;
    private _isInTray: boolean = false;
    private _isMoving: boolean = false;
    private _originalPosition: Vec3 = new Vec3();

    public static onTileTap: Signal<TileMahjong> = new Signal();

    // Get<PERSON> and Setters
    get tileType(): number { return this._tileType; }
    set tileType(value: number) { this._tileType = value; }

    get isSelected(): boolean { return this._isSelected; }
    get isInTray(): boolean { return this._isInTray; }
    set isInTray(value: boolean) {
        this._isInTray = value;
        this.updateTrayState();
    }

    get isMoving(): boolean { return this._isMoving; }

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_START, this.onTap, this);
        this._originalPosition = this.node.position.clone();
    }

    protected onDestroy(): void {
        this.node.off(Node.EventType.TOUCH_START, this.onTap, this);
    }

    private onTap(): void {
        if (this._isInTray) return; // Không cho phép tap khi đã ở trong tray
        if (this._isMoving) return; // Không cho phép tap khi đang di chuyển
        this.bubbleAnimation.playAnimation(this.flyon.bind(this));
    }

    private flyon() {
        TileMahjong.onTileTap?.trigger(this);
    }

    /**
     * Đánh dấu tile được chọn
     */
    setSelected(selected: boolean) {
        this._isSelected = selected;
        if (selected) {
            this.playSelectAnimation();
        }
    }

    /**
     * Animation khi tile được chọn
     */
    private playSelectAnimation() {
        const originalScale = this.visual.scale.clone();
        tween(this.visual)
            .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
            .to(0.1, { scale: originalScale })
            .start();
    }

    /**
     * Animation di chuyển tile đến vị trí mới (sử dụng world position)
     */
    moveTo(targetWorldPosition: Vec3, duration: number = 0.3): Promise<void> {
        return new Promise((resolve) => {
            const parent = this.node.parent;
            if (!parent) {
                console.warn("TileMahjong node has no parent, cannot convert world position");
                resolve();
                return;
            }

            const parentUITransform = parent.getComponent(UITransform);
            if (!parentUITransform) {
                console.warn("Parent node has no UITransform component");
                resolve();
                return;
            }

            // Đánh dấu đang di chuyển
            this._isMoving = true;

            // Chuyển đổi từ world position sang local position của parent
            const localPosition = parentUITransform.convertToNodeSpaceAR(targetWorldPosition);

            tween(this.node)
                .to(duration, {
                    position: localPosition,
                    // scale: new Vec3(0.8, 0.8, 1)
                })
                .call(() => {
                    this._isMoving = false; // Kết thúc di chuyển
                    resolve();
                })
                .start();

            tween(this.visual)
                .to(duration, {
                    scale: new Vec3(0.6, 0.6, 1)
                })
                .start();
        });
    }

    /**
     * Animation biến mất (khi match thành công)
     */
    playDisappearAnimation(delay: number = 0): Promise<void> {
        return new Promise((resolve) => {
            tween(this.visual)
                .delay(delay)
                .to(0.2, { scale: new Vec3(1, 1, 1) })
                .to(0.2, { scale: new Vec3(0, 0, 0) })
                .call(() => {
                    resolve();
                })
                .start();
        });
    }

    /**
     * Reset tile về trạng thái ban đầu
     */
    resetTile() {
        this._tileType = 0;
        this._isSelected = false;
        this._isInTray = false;
        this._isMoving = false;
        this.node.scale = Vec3.ONE.clone();
        this.node.position = this._originalPosition.clone();
        this.node.angle = 0; // Reset rotation

        if (this.tileSprite) this.tileSprite.color = Color.WHITE;
    }

    /**
     * Cập nhật trạng thái khi ở trong tray
     */
    private updateTrayState() {
        if (this._isInTray) {
            this.visual.scale = new Vec3(0.6, 0.6, 1);
            if (this.tileSprite) this.tileSprite.color = Color.WHITE;
        } else {
            this.node.scale = Vec3.ONE.clone();
        }
    }

    /**
     * Di chuyển tile theo đường uốn lượn như bong bóng nổi lên mặt nước
     * @param pos - World position đích
     * @param duration - Thời gian di chuyển (mặc định 2.0s)
     * @param amplitude - Độ lớn của sóng uốn lượn (mặc định 50)
     * @param frequency - Tần số sóng (mặc định 2)
     */
    public startFloat(pos: Vec3, duration: number = 2.0, amplitude: number = 50, frequency: number = 2): Promise<void> {
        return new Promise((resolve) => {
            const parent = this.node.parent;
            if (!parent) {
                console.warn("TileMahjong node has no parent, cannot convert world position");
                resolve();
                return;
            }

            const parentUITransform = parent.getComponent(UITransform);
            if (!parentUITransform) {
                console.warn("Parent node has no UITransform component");
                resolve();
                return;
            }

            // Đánh dấu đang di chuyển
            this._isMoving = true;

            // Chuyển đổi world position sang local position
            const targetLocalPos = parentUITransform.convertToNodeSpaceAR(pos);
            const startPos = this.node.position.clone();

            // Tính toán vector di chuyển
            const deltaX = targetLocalPos.x - startPos.x;
            const deltaY = targetLocalPos.y - startPos.y;

            // Tạo hiệu ứng floating với đường cong
            let currentTime = 0;
            const updateInterval = 0.016; // ~60fps

            const floatTween = () => {
                if (currentTime >= duration) {
                    // Đảm bảo tile đến đúng vị trí cuối
                    this.node.position = targetLocalPos;
                    this._isMoving = false; // Kết thúc di chuyển
                    resolve();
                    return;
                }

                // Tính toán progress (0 -> 1)
                const progress = currentTime / duration;

                // Easing function cho chuyển động mượt mà (ease-out)
                const easedProgress = 1 - Math.pow(1 - progress, 3);

                // Vị trí cơ bản theo đường thẳng
                const baseX = startPos.x + deltaX * easedProgress;
                const baseY = startPos.y + deltaY * easedProgress;

                // Thêm hiệu ứng sóng uốn lượn
                // Sóng ngang (horizontal wave)
                const waveX = Math.sin(progress * Math.PI * frequency) * amplitude * (1 - progress);

                // Sóng dọc nhẹ (vertical bobbing)
                const waveY = Math.sin(progress * Math.PI * frequency * 1.5) * (amplitude * 0.3) * (1 - progress);

                // Hiệu ứng floating lên (như bong bóng nổi)
                const floatY = Math.sin(progress * Math.PI * 0.5) * (amplitude * 0.2);

                // Tính toán vị trí cuối cùng
                const finalX = baseX + waveX;
                const finalY = baseY + waveY + floatY;

                this.node.setPosition(finalX, finalY, startPos.z);

                // Hiệu ứng scale nhẹ khi di chuyển
                const scaleEffect = 1 + Math.sin(progress * Math.PI) * 0.1;
                this.node.scale = new Vec3(scaleEffect, scaleEffect, 1);

                // Hiệu ứng xoay nhẹ
                const rotationEffect = Math.sin(progress * Math.PI * frequency) * 10 * (1 - progress);
                this.node.angle = rotationEffect;

                currentTime += updateInterval;

                // Tiếp tục animation
                this.scheduleOnce(floatTween, updateInterval);
            };

            // Bắt đầu animation
            floatTween();
        });
    }

    /**
     * Hiệu ứng floating đơn giản hơn với bezier curve
     * @param pos - World position đích
     * @param duration - Thời gian di chuyển
     */
    public startFloatBezier(pos: Vec3, duration: number = 1.5): Promise<void> {
        return new Promise((resolve) => {
            const parent = this.node.parent;
            if (!parent) {
                resolve();
                return;
            }

            const parentUITransform = parent.getComponent(UITransform);
            if (!parentUITransform) {
                resolve();
                return;
            }

            // Đánh dấu đang di chuyển
            this._isMoving = true;

            const targetLocalPos = parentUITransform.convertToNodeSpaceAR(pos);
            const startPos = this.node.position.clone();

            // Tạo control points cho bezier curve
            const midX = (startPos.x + targetLocalPos.x) / 2;
            const midY = (startPos.y + targetLocalPos.y) / 2 + 100; // Nâng cao điểm giữa

            const controlPoint1 = new Vec3(
                startPos.x + (midX - startPos.x) * 0.5,
                midY + 50,
                startPos.z
            );

            const controlPoint2 = new Vec3(
                midX + (targetLocalPos.x - midX) * 0.5,
                midY + 30,
                startPos.z
            );

            // Animation với bezier curve sử dụng manual calculation
            let currentTime = 0;
            const updateInterval = 0.016; // ~60fps

            const bezierTween = () => {
                if (currentTime >= duration) {
                    this.node.position = targetLocalPos;
                    this.node.angle = 0; // Reset rotation
                    this._isMoving = false; // Kết thúc di chuyển
                    resolve();
                    return;
                }

                const t = currentTime / duration;
                const invT = 1 - t;

                // Cubic bezier calculation
                const x = invT * invT * invT * startPos.x +
                         3 * invT * invT * t * controlPoint1.x +
                         3 * invT * t * t * controlPoint2.x +
                         t * t * t * targetLocalPos.x;

                const y = invT * invT * invT * startPos.y +
                         3 * invT * invT * t * controlPoint1.y +
                         3 * invT * t * t * controlPoint2.y +
                         t * t * t * targetLocalPos.y;

                this.node.setPosition(x, y, startPos.z);

                // Thêm hiệu ứng xoay nhẹ
                this.node.angle = Math.sin(t * Math.PI * 2) * 15 * (1 - t);

                currentTime += updateInterval;
                this.scheduleOnce(bezierTween, updateInterval);
            };

            bezierTween();
        });
    }

}


