import { _decorator, Component, Node, ParticleSystem2D, UITransform, Vec3, tween } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('FrameEffect')
export class FrameEffect extends Component {
    @property(ParticleSystem2D) particle1: ParticleSystem2D = null!;
    @property(ParticleSystem2D) particle2: ParticleSystem2D = null!;

    @property({ tooltip: "Tốc độ di chuyển (pixel/giây)" })
    moveSpeed: number = 200;

    @property({ tooltip: "Có lặp lại animation không" })
    loop: boolean = true;

    private isAnimating: boolean = false;
    private uiTransform: UITransform = null!;

    protected onLoad(): void {
        this.uiTransform = this.getComponent(UITransform);
        this.playAnimation();
    }

    public playAnimation() {
        this.particle1.resetSystem();
        this.particle2.resetSystem();
        this.startFrameMovement();
    }

    /**
     * B<PERSON>t đầu animation di chuyển theo viền frame
     */
    public startFrameMovement(): void {
        if (this.isAnimating) {
            this.stopFrameMovement();
        }

        this.isAnimating = true;

        // Cả 2 particle đều di chuyển theo chiều kim đồng hồ
        // Particle 1 bắt đầu từ góc trên-trái
        this.moveParticleAroundFrame(this.particle1.node, 0);

        // Particle 2 bắt đầu từ góc dưới-phải (đối diện particle 1)
        this.moveParticleAroundFrame(this.particle2.node, 2);
    }

    /**
     * Dừng animation di chuyển
     */
    public stopFrameMovement(): void {
        this.isAnimating = false;
        tween(this.particle1.node).stop();
        tween(this.particle2.node).stop();
    }

    /**
     * Di chuyển particle theo viền frame
     */
    private moveParticleAroundFrame(particleNode: Node, startIndex: number): void {
        if (!this.uiTransform) return;

        const width = this.uiTransform.width;
        const height = this.uiTransform.height;
        const halfWidth = width / 2;
        const halfHeight = height / 2;

        // Tính toán các điểm góc của frame (theo chiều kim đồng hồ)
        const corners = [
            new Vec3(-halfWidth, halfHeight, 0),   // 0: Top-left
            new Vec3(halfWidth, halfHeight, 0),    // 1: Top-right
            new Vec3(halfWidth, -halfHeight, 0),   // 2: Bottom-right
            new Vec3(-halfWidth, -halfHeight, 0)   // 3: Bottom-left
        ];

        // Tính thời gian di chuyển cho mỗi cạnh
        const topBottomTime = width / this.moveSpeed;
        const leftRightTime = height / this.moveSpeed;

        // Đặt vị trí bắt đầu
        particleNode.position = corners[startIndex].clone();

        // Tạo chuỗi animation
        this.createFrameAnimation(particleNode, corners, topBottomTime, leftRightTime, startIndex);
    }

    /**
     * Tạo animation di chuyển theo từng cạnh của frame
     */
    private createFrameAnimation(
        particleNode: Node,
        corners: Vec3[],
        topBottomTime: number,
        leftRightTime: number,
        startIndex: number
    ): void {
        const times = [topBottomTime, leftRightTime, topBottomTime, leftRightTime];

        let tweenChain = tween(particleNode);

        // Tạo sequence di chuyển từ vị trí hiện tại theo chiều kim đồng hồ
        for (let i = 1; i <= 4; i++) {
            const nextIndex = (startIndex + i) % 4;
            const timeIndex = (startIndex + i - 1) % 4;
            tweenChain = tweenChain.to(times[timeIndex], { position: corners[nextIndex] }, { easing: 'linear' });
        }

        // Nếu loop, lặp lại animation
        if (this.loop) {
            tweenChain = tweenChain.call(() => {
                if (this.isAnimating) {
                    this.createFrameAnimation(particleNode, corners, topBottomTime, leftRightTime, startIndex);
                }
            });
        } else {
            tweenChain = tweenChain.call(() => {
                if (particleNode === this.particle2.node) {
                    this.isAnimating = false; // Dừng khi particle cuối cùng hoàn thành
                }
            });
        }

        tweenChain.start();
    }

    /**
     * Kiểm tra xem có đang animation không
     */
    public isPlaying(): boolean {
        return this.isAnimating;
    }

    /**
     * Thiết lập tốc độ di chuyển mới
     */
    public setMoveSpeed(speed: number): void {
        this.moveSpeed = speed;

        // Nếu đang chạy, restart với tốc độ mới
        if (this.isAnimating) {
            this.startFrameMovement();
        }
    }
}


