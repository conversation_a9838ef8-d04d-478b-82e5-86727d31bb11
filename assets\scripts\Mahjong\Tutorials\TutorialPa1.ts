import { _decorator, CCInteger, Component, Node } from 'cc';
import { TileMahjong } from '../TileMahjong';
const { ccclass, property } = _decorator;

@ccclass('TutorialPa1')
export class TutorialPa1 extends Component {
    @property(CCInteger) public tutorialType = 0;
    @property(Node) public contain: Node = null!;
    @property(Node) public bg: Node = null!;

    private countTile = 3;

    public arrTile: TileMahjong[] = [];

    public isActive: boolean = false;

    public timer = 0;
    private timerMax = 3;

    public decreaseCount() {
        this.countTile--;
        this.countTile = Math.max(0, this.countTile);

        if (this.countTile === 0) {
            this.bg.active = false;
            this.isActive = false;
        }
    }

    update(deltaTime: number) {
        if (!this.isActive) return;

        this.timer += deltaTime;
        if (this.timer >= this.timerMax) {
            this.isActive = false;
            this.showTutorial();
        }
    }


    public showTutorial() {

        for (let i = 0; i < this.arrTile.length; i++) {
            const tile = this.arrTile[i];
            if (!tile.isInTray) {
                tile.showTutorial();
                tile.playGrowFx();
                break;
            }
        }
    }

    public activeTutorial() {
        this.isActive = true;
        this.timer = 0;
    }



}


