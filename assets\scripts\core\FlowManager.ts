import { _decorator, Component, Node } from 'cc';
import { GameManager } from './GameManager';
import { GameState } from '../enum/Enums';
const { ccclass, property } = _decorator;

const mraid = window['mraid'] || null;

@ccclass('GameFlow')
export class GameFlow extends Component {
    public static isLoaded = false;
    public static instance: GameFlow = null;

    protected onLoad(): void {
        GameFlow.instance = this;
        window['gameReady'] && window['gameReady']();
    }

    protected start(): void {
        this.waitSettingUp(window['adsChannels']);
    }

    waitSettingUp(ad_network: string): void {
        const actions = {
            Unity: () => mraid ? this.initUnity() : this.init(),
            Mintegral: () => GameFlow.isLoaded && this.init(),
            default: () => this.init()
        };

        console.log(`init case ${ad_network || 'Default'}`);
        (actions[ad_network] || actions.default)();
    }

    initUnity(): void {
        if (mraid) {
            this.init();
            return;
        }

        mraid.addEventListener('ready', () => {
            mraid?.isViewable() ? this.init() :
                mraid?.addEventListener('viewableChange', () => this.init());
        });
    }

    init(): void {
        GameManager.Instance.setGameState(GameState.Prepare);
    }
}


window['advChannels'] = "{{__adv_channels_adapter__}}";

window["gameStart"] = function () {
    if (!GameFlow.instance) {
        console.log('init case Mintegral early');
        GameFlow.isLoaded = true;
    } else {
        console.log('init case Mintegral lately');
        GameFlow.instance.init();
    }
};




