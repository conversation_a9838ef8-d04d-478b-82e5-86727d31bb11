import { _decorator, AnimationComponent, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('BubbleAnimation')
export class BubbleAnimation extends Component {

    @property(AnimationComponent) private anim;

    public playAnimation(callback?: () => void) {
        this.anim.play();

        callback && this.anim.once('finished', () =>{
            callback();
            this.node.active = false;
        });
    }
}


