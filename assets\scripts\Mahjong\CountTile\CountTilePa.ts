import { _decorator, Component, Node, sys } from 'cc';
import { TileMahjong } from '../TileMahjong';

const { ccclass, property } = _decorator;

@ccclass('CountTilePa')
export class CountTilePa extends Component {

    @property(Node) private touchZone: Node = null!;

    private count = 8;

    protected start(): void {
        TileMahjong.onTileTap.on(this.decreaseCount, this);
    }


    public decreaseCount() {
        this.count--;
        this.count = Math.max(0, this.count);

        if (this.count === 0) {
            this.cta();
            this.touchZone.active = true;
        }
    }

    cta() {


        const ad_network = window['advChannels'];
        if (!!ad_network) {
            switch (ad_network) {
                case "Mintegral": {
                    window['install'] && window['install']();
                    return;
                }
                case "Unity":
                case "AppLovin": {
                    let open;
                    if (sys.os == sys.OS.ANDROID) {
                        open = window["mraidOpenPlayStore"];
                    } else {
                        open = window["mraidOpenAppStore"];
                    }
                    open?.();
                    return;
                };
            }
        }
        sys.openURL("https://play.google.com/store/apps/details?id=com.legendarylabs.triple.mahjong.royal.victorian");
    }

}


