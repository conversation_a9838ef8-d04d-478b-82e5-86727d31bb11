[{"__type__": "cc.SceneAsset", "_name": "game", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "game", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_active": true, "_components": [], "_prefab": {"__id__": 264}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 269}, "_id": "835201ea-1177-41cd-9ad1-4d33005620de"}, {"__type__": "cc.Node", "_name": "GameFlow", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "512XmUOGdCcLFLjIUZAcYq"}, {"__type__": "17554D+auRPZZ8E2+wmKrbn", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_id": "72xqNkyItEDL8q9Zl0KN49"}, {"__type__": "cc.Node", "_name": "GameManager", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 5}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "07OBT48A9Do69YylwRypWU"}, {"__type__": "97f8dfSqKBOCJi/6u0FMsKR", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "__prefab": null, "_id": "cfDUBtk+FO75OGzAnP0PYz"}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "62F6ehl3lCZo8CYUdTpstF"}, {"__type__": "984d3sCegdCzb3aCXT2my21", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "__prefab": null, "BGM": {"__uuid__": "81f2efe3-e0da-49f3-acaa-154f9b25a873", "__expectedType__": "cc.AudioClip"}, "Button_Click": {"__uuid__": "d1fed64d-011c-4bd7-9818-0ebde2f74a38", "__expectedType__": "cc.AudioClip"}, "Win": {"__uuid__": "9b46b6d6-29e8-4aa7-9e8a-f0b84a6e84fa", "__expectedType__": "cc.AudioClip"}, "Lose": {"__uuid__": "771a920a-9d0d-45b9-9b65-f5aebc0635ee", "__expectedType__": "cc.AudioClip"}, "Match": {"__uuid__": "06cae826-b324-4517-a52b-5cb02efc2a4d", "__expectedType__": "cc.AudioClip"}, "Popup": {"__uuid__": "a3c1f938-889d-4240-bd53-5b4538517fe1", "__expectedType__": "cc.AudioClip"}, "_id": "13xNk0VPxHxaE5NT11o943"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 9}, {"__id__": 11}, {"__id__": 15}], "_active": true, "_components": [{"__id__": 261}, {"__id__": 262}, {"__id__": 263}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 540, "y": 960, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 10}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 960, "_near": 0, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "bg-01", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 13}, {"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d5P2MfYXFE0aYMVp7vciDl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "34VrwBunZES55Fgyfgg6UW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 45, "_id": "cc/YtsJmBCu44LbFWMT7Nu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 41, "g": 41, "b": 41, "a": 255}, "_spriteFrame": {"__uuid__": "7321f17a-7536-48e1-82cb-fc49fe63e0d2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "989/HnTL9LS4JDT7kgHJHi"}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [{"__id__": 16}, {"__id__": 20}, {"__id__": 127}, {"__id__": 132}, {"__id__": 168}, {"__id__": 189}, {"__id__": 136}], "_active": true, "_components": [{"__id__": 259}, {"__id__": 260}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7cOrmcB7FM7IRsFmkg5Afe"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 17}, {"__id__": 18}, {"__id__": 19}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "95ybdQPflFdJiKj9Z7WySz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "57lAteZe5HSZYn6uJRL1L9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4ff209cd-7634-4ea9-b36e-f1ec99d423ee@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "aaqSLEWhJKXJmGk6fZjP87"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1080, "_originalHeight": 1920, "_alignMode": 2, "_lockFlags": 45, "_id": "13GZbQmHNJBr6q2Jaiqzwg"}, {"__type__": "cc.Node", "_name": "Top", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 21}, {"__id__": 25}, {"__id__": 67}, {"__id__": 96}], "_active": true, "_components": [{"__id__": 125}, {"__id__": 126}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 760, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "21oJfHAuRL6ZFfz6k+yBzB"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 22}, {"__id__": 23}, {"__id__": 24}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "22XcZtGvdNcbcebmOKA9co"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7biFoUYDZNla/5siUrwgeJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "47fae19a-ef2a-4274-9453-8ebc454bccd8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "fd9dACJ3VHxadi3Y1hozBZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 32, "_originalHeight": 406, "_alignMode": 2, "_lockFlags": 45, "_id": "fcPTc5xaRD4qtbGooUUmYo"}, {"__type__": "cc.Node", "_name": "Tray", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [{"__id__": 26}, {"__id__": 31}, {"__id__": 36}, {"__id__": 41}, {"__id__": 46}, {"__id__": 51}, {"__id__": 56}], "_active": true, "_components": [{"__id__": 61}, {"__id__": 62}, {"__id__": 63}, {"__id__": 64}, {"__id__": 65}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 159, "y": -48.932000000000016, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c9xw73II5HU5UfTM/oG1kA"}, {"__type__": "cc.Node", "_name": "TraySlot_00", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 27}], "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -277, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "27tY2qEfhJ/5haUbUZI6M5"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [{"__id__": 28}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "57LOQB1IBMKK/Tj7Q6YKUx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "77xg95YkdF/Y2KVR8Flu12"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "22hnlYZXtO6IVTCQ9RxD6L"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9932ae3c-e99c-4c81-8072-efa805a9a411@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c7p77rD5hCD653owE/VdLX"}, {"__type__": "cc.Node", "_name": "TraySlot_01", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 32}], "_active": true, "_components": [{"__id__": 34}, {"__id__": 35}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -185, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4edjOJquZE/aCOZ1VIaRf9"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": true, "_components": [{"__id__": 33}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "deSg33MZFPjaxcBOgqlIIy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "98liRcjUBGcYLUe3xpPlSU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e0Gaep94dJxo0yjRLKSJbf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9932ae3c-e99c-4c81-8072-efa805a9a411@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "dbWUjVzoFOb7N/9SKgJVVA"}, {"__type__": "cc.Node", "_name": "TraySlot_02", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 37}], "_active": true, "_components": [{"__id__": 39}, {"__id__": 40}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -93, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b92aWxrdpCZYrZocwnMQpk"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 38}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "312ozcC31G3Lg0c3RtgkUN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7cxt5BEvdMVIpEzFnQ5FTy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7b683okrxMzbBug5HAPBdy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9932ae3c-e99c-4c81-8072-efa805a9a411@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "eav7i7jVVK5q+fPpSdsRWX"}, {"__type__": "cc.Node", "_name": "TraySlot_03", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 42}], "_active": true, "_components": [{"__id__": 44}, {"__id__": 45}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -1, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "61aTgP2dtGYZ8LL6TZvS02"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [], "_active": true, "_components": [{"__id__": 43}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d1rb0Z/txEz5doS3gTjgO2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "10AHb3fM1E7poJrXRlgmAl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "20q4VAJY1CSqWgMvjBMM0O"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9932ae3c-e99c-4c81-8072-efa805a9a411@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6bZRPgnhxE0JhmM1CNkHvg"}, {"__type__": "cc.Node", "_name": "TraySlot_04", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 47}], "_active": true, "_components": [{"__id__": 49}, {"__id__": 50}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 91, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "73Zb7kjTJIJrD3iUY49zZy"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [{"__id__": 48}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b5IHzfc3JLTasI9EXlhaZU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "49Vg5gwdVERbtADA+G9LSn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "56iS9KZYpMtpSRPEBjWAWS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9932ae3c-e99c-4c81-8072-efa805a9a411@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8e5M367jlKs4acLdLD7geI"}, {"__type__": "cc.Node", "_name": "TraySlot_05", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 52}], "_active": true, "_components": [{"__id__": 54}, {"__id__": 55}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 183, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "28BRRWsn9BP70zrzf5BEOO"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 51}, "_children": [], "_active": true, "_components": [{"__id__": 53}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "392MbTYHJLfr7Ba7w3egUW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8dJU94U0BD+qzmsMvARA21"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3eHviE8kRHcITxQe5LQQcM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9932ae3c-e99c-4c81-8072-efa805a9a411@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "aabX7tX1hCx5VM2UUFixS2"}, {"__type__": "cc.Node", "_name": "TraySlot_06", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 57}], "_active": true, "_components": [{"__id__": 59}, {"__id__": 60}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 275, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f2j2+UQxtHhoB69EeimQZi"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 56}, "_children": [], "_active": true, "_components": [{"__id__": 58}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "83m7NcdEtIOKzsH8FEepUD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2fQT77X5dGXaS4oWLL6+Or"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fflknCqp9EIaiFPeqf7dnj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9932ae3c-e99c-4c81-8072-efa805a9a411@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3f29jkCCpN9YLM/A3yFIxi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 690, "height": 156}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a5rXEoWWxInKR9RnPpuO6o"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "602f9818-c7e0-4d8d-a1c7-d46cb7aea52f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "120/B0wftFJYfuTtWWVWe7"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 24, "_paddingRight": 26, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 4, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "a9Vj/POUlL/Z1eca1ElRpE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 36, "_top": 170.93200000000002, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 32, "_id": "0a1Ac3+vpJooULGszvodNi"}, {"__type__": "ef8384eW2pJpZDXm2mtbfAk", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "progressBar": {"__id__": 66}, "_id": "e0LKnVADFBeIj/f5oUgIzU"}, {"__type__": "4330bNz98xFL7mQC9gUQAn0", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "progressBar": {"__id__": 74}, "animationDuration": 0.5, "_id": "b5Xe5Vfu1LFrSiqFe+0OQf"}, {"__type__": "cc.Node", "_name": "Progress", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [{"__id__": 68}, {"__id__": 72}, {"__id__": 75}, {"__id__": 81}, {"__id__": 87}], "_active": true, "_components": [{"__id__": 93}, {"__id__": 94}, {"__id__": 66}, {"__id__": 95}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 158.079, "y": 95.214, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5985opyjFInKcI6go0m1pU"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [{"__id__": 69}, {"__id__": 70}, {"__id__": 71}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bamJsZ9XpPVb1ffpP7gkjK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9cIQQuneRGfbQFcFX2/KQU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "42a8c355-9a8e-4a02-86e9-6b3e0b2bbef3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "67zbTanX5FNJLGb+3VaI76"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "__prefab": null, "_alignFlags": 0, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "2cHX+CQCRKcYBrSpHi+ESt"}, {"__type__": "cc.Node", "_name": "fg", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [{"__id__": 73}, {"__id__": 74}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1.745, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "336u6YYtxC4YSgXBngEYTm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1dMRwfPiZCBowpIp0H5aUk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "286760d7-5de6-4eeb-bb67-88fd13275eb0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "52RM6QXoZKVaGMDKlHsLfw"}, {"__type__": "cc.Node", "_name": "star_0", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [{"__id__": 76}], "_active": true, "_components": [{"__id__": 79}, {"__id__": 80}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -210, "y": 26.895, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "33Twm49VJC45x5IFq4+SmP"}, {"__type__": "cc.Node", "_name": "star<PERSON>ame", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 77}, {"__id__": 78}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "75/v0N7yBA47z5DnxhMIvN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "81uz4wwXNN5IFnDkwyciO4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2b52467d-261c-49d6-a4be-329933a1dca3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "45Bi5yNqBKP4zj+PLW6bSg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "06AhNIVC9ENIhCrnQSVpZb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2744e39a-c6f8-4262-930b-32464650772c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "2aNpss+eBJ+ZkEUx+x2FFk"}, {"__type__": "cc.Node", "_name": "star_1", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [{"__id__": 82}], "_active": true, "_components": [{"__id__": 85}, {"__id__": 86}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 26.895, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1dYKRU4xhCoqcBh+o59Mr3"}, {"__type__": "cc.Node", "_name": "star<PERSON>ame", "_objFlags": 0, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 84}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "8cY/hgyFROx7HvOFr2F2pB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "85njZ4B1FMILw8Xy1d6dMu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2b52467d-261c-49d6-a4be-329933a1dca3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6bxd97BVhMyY+gYmqCAlnR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b5WTW+dJBA96G4DVNKgxre"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2744e39a-c6f8-4262-930b-32464650772c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a7/CQ2xq1J3YPPmc33cNOh"}, {"__type__": "cc.Node", "_name": "star_2", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [{"__id__": 88}], "_active": true, "_components": [{"__id__": 91}, {"__id__": 92}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 280, "y": 26.895, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "17rcNLEMJAk5kaFAqSY+au"}, {"__type__": "cc.Node", "_name": "star<PERSON>ame", "_objFlags": 0, "_parent": {"__id__": 87}, "_children": [], "_active": true, "_components": [{"__id__": 89}, {"__id__": 90}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c9jh0yKt5Bq7YuAaoZAhsx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "82vPOsVF1MxrsKrc2yeaBU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2b52467d-261c-49d6-a4be-329933a1dca3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "920jOwGr5GMKYntx4a/kgF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1ccuApf2BG7rrUgKsOnIH5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2744e39a-c6f8-4262-930b-32464650772c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "19slvc9U5HmrwAshA1kpeg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8cjB2lA5FI4bHVvfz9b31t"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "_alignFlags": 0, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "af0MKVlfVKxbMH+O/63uRK"}, {"__type__": "d2e35EeTrRB74ShTdL0VTYb", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "targetScore": [20, 50, 90], "starNodes": [{"__id__": 76}, {"__id__": 82}, {"__id__": 88}], "starDelay": 0.2, "starAnimationDuration": 0.5, "starPopScale": 1.2, "_id": "dfzpQNuVZMqoVFZMNF/jq0"}, {"__type__": "cc.Node", "_name": "avaGame", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [{"__id__": 97}, {"__id__": 100}], "_active": true, "_components": [{"__id__": 122}, {"__id__": 123}, {"__id__": 124}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -365.5, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0dDLY0hDVMtqejAvjruaqq"}, {"__type__": "cc.Node", "_name": "Tung-Tung-Tung-Sahur-Brainrot-Trend", "_objFlags": 0, "_parent": {"__id__": 96}, "_children": [], "_active": true, "_components": [{"__id__": 98}, {"__id__": 99}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.38, "y": 0.38, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "067kTSrEtNK50fthNUCxQ6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 206, "height": 485}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "11EaUJwS9PsLcVMkdI34BM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4b1cbb78-f235-4b94-ad5d-cdc78ff4579a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "derL9wqa1BqpWLi1a/S1Sh"}, {"__type__": "cc.Node", "_name": "FrameFx", "_objFlags": 0, "_parent": {"__id__": 96}, "_children": [{"__id__": 101}, {"__id__": 112}], "_active": true, "_components": [{"__id__": 120}, {"__id__": 121}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0.5904999999999996, "y": -2.9524999999999997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2cANpiSA9L8rDLQ4v02IiB"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 100}, "_prefab": {"__id__": 102}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 101}, "asset": {"__uuid__": "9d87a7c0-7be1-465f-8179-56773d43a16a", "__expectedType__": "cc.Prefab"}, "fileId": "b5QrmKrrlKfZ6Ikto0ze9F", "instance": {"__id__": 103}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "19jytY+EFFEqVMYhhtK0ys", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 104}, {"__id__": 106}, {"__id__": 107}, {"__id__": 108}, {"__id__": 109}, {"__id__": 111}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_name"], "value": "Particle2D"}, {"__type__": "cc.TargetInfo", "localID": ["b5QrmKrrlKfZ6Ikto0ze9F"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -112.956, "y": -114.791, "z": -1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 110}, "propertyPath": ["speed"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["ccxu7OtEhFl7ltK4t+NlDo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 110}, "propertyPath": ["speedVar"], "value": 100}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 100}, "_prefab": {"__id__": 113}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 112}, "asset": {"__uuid__": "9d87a7c0-7be1-465f-8179-56773d43a16a", "__expectedType__": "cc.Prefab"}, "fileId": "b5QrmKrrlKfZ6Ikto0ze9F", "instance": {"__id__": 114}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "4ezXpktOhDT4BFLkodPU2m", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 115}, {"__id__": 117}, {"__id__": 118}, {"__id__": 119}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_name"], "value": "Particle2D-001"}, {"__type__": "cc.TargetInfo", "localID": ["b5QrmKrrlKfZ6Ikto0ze9F"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -112.956, "y": -114.791, "z": -1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 239.475, "height": 229.19899999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "01km3Sg1xHu6JJRc/7GnoK"}, {"__type__": "c3e4cXb1tlM7piYjrjVFO37", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "__prefab": null, "particle1": null, "particle2": null, "moveSpeed": 200, "loop": true, "_id": "d2RJ5tjeZDHqbCAVlIU8Io"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 269, "height": 254}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a16qPlQb1LravvfYL2g2DA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "78ad1706-5693-4fcd-9eea-2c1ceea7e11f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4aVby14n9CFZLORMWMalOK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": null, "_left": 40, "_right": 0, "_top": 72, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 9, "_id": "96YHIfKnZG575jbHSQ+7gs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "584f31lopHtbTkPQNfS4zA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 41, "_id": "44FgNc6AxCBKdxcBkuSxvv"}, {"__type__": "cc.Node", "_name": "Character", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 128}], "_active": true, "_components": [{"__id__": 131}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "56iMTBVxVMPa+oQutktbOr"}, {"__type__": "cc.Node", "_name": "Tung-Tung-Tung-Sahur-Brainrot-Trend", "_objFlags": 0, "_parent": {"__id__": 127}, "_children": [], "_active": true, "_components": [{"__id__": 129}, {"__id__": 130}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -20, "y": -6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.8, "y": 1.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "aaCk88sJ5PQLOWp80q/Q/V"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 206, "height": 485}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c5a+noHAhOno00kDmuoq+S"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 100}, "_spriteFrame": {"__uuid__": "4b1cbb78-f235-4b94-ad5d-cdc78ff4579a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "cdCe6jaz9HLrSh0QYjunKh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "77Vd6ouk9IXY/dhLEfV8Vn"}, {"__type__": "cc.Node", "_name": "MapLoader", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 133}, {"__id__": 134}, {"__id__": 167}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -150, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "actjtvG5VCP4gJO9l6eQxf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 132}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1620}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "26/ku+KYVLe7F6aVd8h3qN"}, {"__type__": "29ef2MyebdPLLsleYQDvT6Z", "_name": "", "_objFlags": 0, "node": {"__id__": 132}, "_enabled": true, "__prefab": null, "tilePrefab": {"__uuid__": "b1e4c259-76d2-47e7-a9dd-779209349b91", "__expectedType__": "cc.Prefab"}, "layerPrefab": {"__uuid__": "d2b1067e-bed8-4cf0-a5f9-35fd89247306", "__expectedType__": "cc.Prefab"}, "levelData": {"__uuid__": "9027954e-16cc-4c0b-96c1-5a0a1851f65b", "__expectedType__": "cc.Json<PERSON>set"}, "tutorial": {"__id__": 135}, "currentLevel": 1, "_id": "e75Oud2atIKqdi0LDIMwEK"}, {"__type__": "fa2f1xb6KlHWYCgSqdMkFv8", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "__prefab": null, "hand": {"__id__": 146}, "stepNodes": [{"__id__": 142}, {"__id__": 162}], "stepTilePos": [], "tray": {"__id__": 65}, "_id": "2bPzMyHMdMyY7fYzpXwU+3"}, {"__type__": "cc.Node", "_name": "Tutorial", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 137}, {"__id__": 142}, {"__id__": 145}, {"__id__": 162}], "_active": false, "_components": [{"__id__": 165}, {"__id__": 166}, {"__id__": 135}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d31Xy96v1IRbk5Iu93DeyO"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [], "_active": true, "_components": [{"__id__": 138}, {"__id__": 139}, {"__id__": 140}, {"__id__": 141}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eeh5OLd51MdJKwoDYztRux"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0f/YVcS/lHK697MI4cqmJN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 201}, "_spriteFrame": {"__uuid__": "7321f17a-7536-48e1-82cb-fc49fe63e0d2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "baTKLcreRFlqJCkPQFLhJ+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1, "_originalHeight": 1, "_alignMode": 2, "_lockFlags": 0, "_id": "64yWkc6q1HZYJT4t6NveLH"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "7321f17a-7536-48e1-82cb-fc49fe63e0d2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "39QowvM85DfpSMkBgj792D"}, {"__type__": "cc.Node", "_name": "step1", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [], "_active": true, "_components": [{"__id__": 143}, {"__id__": 144}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -150, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "affXQmFoVJqKhXjtZAwqtN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1620}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f5qjIVaBJJf4SKFIHAkiPP"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 300, "_bottom": -1.4210854715202004e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 884, "_originalHeight": 10, "_alignMode": 2, "_lockFlags": 45, "_id": "db2HwtdzRH4KB+fREFGnDC"}, {"__type__": "cc.Node", "_name": "tutorial", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 136}, "_children": [{"__id__": 146}], "_active": true, "_components": [{"__id__": 161}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -312, "y": -428, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1.6}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7cOsqcG31JCZ+Npo7lSdcV"}, {"__type__": "cc.Node", "_name": "hand", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 145}, "_children": [{"__id__": 147}, {"__id__": 151}, {"__id__": 155}], "_active": true, "_components": [{"__id__": 158}, {"__id__": 159}, {"__id__": 160}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 50.893, "y": -21.926, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 0.3}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "709nEQkTdBv5ZUU94Rhnvm"}, {"__type__": "cc.Node", "_name": "ring1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 146}, "_children": [], "_active": true, "_components": [{"__id__": 148}, {"__id__": 149}, {"__id__": 150}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "16TjCN1S5E15YlfbEWCkwB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 164}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "546fWsnkRJoZ1pvKePHz3E"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6c3b39d4-d574-436a-8ae5-c6d955925a92@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e68BwDzQFMgaxUJEGnCO6C"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": null, "_opacity": 0, "_id": "08ML3yH2FOjrwRyKDq5GQQ"}, {"__type__": "cc.Node", "_name": "ring2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 146}, "_children": [], "_active": true, "_components": [{"__id__": 152}, {"__id__": 153}, {"__id__": 154}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "84dKap/5BBvpe0EOkvfPH/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 164}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "500NjOlBdAiaWng24SOUMV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6c3b39d4-d574-436a-8ae5-c6d955925a92@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b9KotoIjpJRY9I1qPz0o4l"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": null, "_opacity": 0, "_id": "a13kPJuIhMr51jHTMAHfmt"}, {"__type__": "cc.Node", "_name": "Hand", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 146}, "_children": [], "_active": true, "_components": [{"__id__": 156}, {"__id__": 157}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.21643961393810288, "w": 0.9762960071199334}, "_lscale": {"__type__": "cc.Vec3", "x": 0.45, "y": 0.45, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 25}, "_id": "61f5fhjwtMP7gQFuqWFOTM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 323, "height": 477}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.25, "y": 0.85}, "_id": "a0OTV2TAZIHK3fX+hYX+Pb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 155}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fa7bd01b-4f00-43d4-9fdb-0d3edfab4377@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "eeTkUtOktMu49eF61T1d69"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e9g0gLdqFBbpDrMRG7cKw5"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": null, "playOnLoad": true, "_clips": [{"__uuid__": "0ddbd80e-5b13-41ac-b286-d7721e35cf57", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "0ddbd80e-5b13-41ac-b286-d7721e35cf57", "__expectedType__": "cc.AnimationClip"}, "_id": "beJRhU36dARovdWmvDtu6w"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "4ai0EECqVAMofG8LmVYJ1d"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "22rkTuHNdN85qvoECrLz++"}, {"__type__": "cc.Node", "_name": "step2", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [], "_active": true, "_components": [{"__id__": 163}, {"__id__": 164}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -150, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "71ZAY9dypKgZyt7DmOynZI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1620}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "94sM3xVShD/7dvGyyI8uNC"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 300, "_bottom": -1.4210854715202004e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 884, "_originalHeight": 10, "_alignMode": 2, "_lockFlags": 45, "_id": "f7PJ08SLZGtIWNL75dPuzD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d1jetPkydDh5HJIngPYkEr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -1.4210854715202004e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 884, "_originalHeight": 10, "_alignMode": 2, "_lockFlags": 45, "_id": "32VgES0UVHKLutupedzp+K"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 132}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 300, "_bottom": -1.4210854715202004e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 884, "_originalHeight": 10, "_alignMode": 2, "_lockFlags": 45, "_id": "dfsXvJ4x9M4ocKL6tRXvlK"}, {"__type__": "cc.Node", "_name": "Bottom", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 169}], "_active": true, "_components": [{"__id__": 187}, {"__id__": 188}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -760, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e9xdu7ppRB6a9CDtMSS/HW"}, {"__type__": "cc.Node", "_name": "bg-special-event-icon", "_objFlags": 0, "_parent": {"__id__": 168}, "_children": [{"__id__": 170}, {"__id__": 173}, {"__id__": 176}, {"__id__": 180}], "_active": true, "_components": [{"__id__": 183}, {"__id__": 184}, {"__id__": 185}, {"__id__": 186}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "553+aGUnZEzrk9dq8oHSKh"}, {"__type__": "cc.Node", "_name": "square-cash", "_objFlags": 0, "_parent": {"__id__": 169}, "_children": [], "_active": true, "_components": [{"__id__": 171}, {"__id__": 172}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -360.129, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6dIUGzUi5JH5v8v8cVWwV2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "25TZYF4TRBxLTE5OAWzN4W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "47a881f2-2138-4041-a224-29647567c255@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "18hu37eZBAE4BCA5rvKO7Q"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "_parent": {"__id__": 169}, "_children": [], "_active": true, "_components": [{"__id__": 174}, {"__id__": 175}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 197.6055, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4dbJvWWu5BNIeOvkVHVLYA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 444.625, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "02MvWsAQlJwL9yomX2tFYO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 173}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 172}, "_spriteFrame": {"__uuid__": "b473a85b-f47d-4e0e-97b0-4f9563ea1825@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e3h7tNQ4ZPZaKcod8mej72"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 169}, "_children": [], "_active": true, "_components": [{"__id__": 177}, {"__id__": 178}, {"__id__": 179}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -224.668, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0dMu3tQXpPn5vn/wg1J6mq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 176}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 132.41, "height": 77.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "da0BlnA85MkrATMhr9nbO8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 176}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Cash", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 58, "_fontSize": 58, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 58, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "911a9cbd-c991-48bf-821d-c52a2857ad6c", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "71GITblBVDvqGQxlpTpaC6"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 176}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 37, "g": 44, "b": 199, "a": 255}, "_width": 2, "_id": "4asDaLraFPNpnLrZO8PJrJ"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "_parent": {"__id__": 169}, "_children": [], "_active": true, "_components": [{"__id__": 181}, {"__id__": 182}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 197.6055, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "05HEc5GNhMCJ/bgzlwuayN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 380, "height": 73.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "05+kFE+/FDxoRSYrdjWwDB"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "$0", "_horizontalAlign": 2, "_verticalAlign": 1, "_actualFontSize": 58, "_fontSize": 58, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 58, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "911a9cbd-c991-48bf-821d-c52a2857ad6c", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "85nuSxbg9NJ566G33B/Bjv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 169}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 909.808, "height": 180}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "40xxs5gnNKnK49s49cb/mg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 169}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cd09b865-2ab8-497b-b92c-b482b3902c0c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "1fDyItvfZF9qEwxWePkDFW"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 169}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 80, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "76qbcUvvhJeblfMO8GyQgf"}, {"__type__": "f8968qFGndNGYgzoUNdKgnc", "_name": "", "_objFlags": 0, "node": {"__id__": 169}, "_enabled": true, "__prefab": null, "lblCash": {"__id__": 182}, "value": 300, "progressBar": {"__id__": 66}, "_id": "50CnymolZKWqbaj7FD2xMS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e3SK/OkSRGXan77rsViiPP"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "__prefab": null, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 44, "_id": "dc2qihpXBFA494kTxfHw2X"}, {"__type__": "cc.Node", "_name": "PopupManager", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 190}, {"__id__": 214}], "_active": true, "_components": [{"__id__": 256}, {"__id__": 257}, {"__id__": 258}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ecgiN9CE9N1IMsUiGrBnJr"}, {"__type__": "cc.Node", "_name": "PopupEndScreen", "_objFlags": 0, "_parent": {"__id__": 189}, "_children": [{"__id__": 191}, {"__id__": 195}, {"__id__": 198}], "_active": false, "_components": [{"__id__": 210}, {"__id__": 211}, {"__id__": 212}, {"__id__": 213}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97DqjHBfhPfII2ZczpQNYq"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 190}, "_children": [], "_active": true, "_components": [{"__id__": 192}, {"__id__": 193}, {"__id__": 194}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d1+vCu/1NFUL05Rq/fJvBt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1eBbPTgYJN6pfSlwWVYVGD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 200}, "_spriteFrame": {"__uuid__": "7321f17a-7536-48e1-82cb-fc49fe63e0d2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "fbC9B2U7lFmbt02OVd/4P3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1, "_originalHeight": 1, "_alignMode": 2, "_lockFlags": 0, "_id": "67FN4/Z3VAmrjVbMRQQl6O"}, {"__type__": "cc.Node", "_name": "spr_bgLogo_v1", "_objFlags": 0, "_parent": {"__id__": 190}, "_children": [], "_active": true, "_components": [{"__id__": 196}, {"__id__": 197}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 184.311, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e5Ov4Aq+JIx5q9krkp6ley"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1094, "height": 509}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e8t9u7nBBHE4LG3TXul3c6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "47a7ef4d-a930-48d8-8851-6dfbb6c4cdad@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "3560XaMyNBPLvzlKG61+ON"}, {"__type__": "cc.Node", "_name": "ContainButtom", "_objFlags": 0, "_parent": {"__id__": 190}, "_children": [{"__id__": 199}], "_active": true, "_components": [{"__id__": 208}, {"__id__": 209}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -452.927, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 2, "y": 2, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "56m4BKeJFMG5rotmeGGZQc"}, {"__type__": "cc.Node", "_name": "btn_green-001", "_objFlags": 0, "_parent": {"__id__": 198}, "_children": [{"__id__": 200}], "_active": true, "_components": [{"__id__": 204}, {"__id__": 205}, {"__id__": 206}, {"__id__": 207}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e3IG29ZdNFD7n5GhYQECxW"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 199}, "_children": [], "_active": true, "_components": [{"__id__": 201}, {"__id__": 202}, {"__id__": 203}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 4.184, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d6CI3nqSZFaa4V/yNrXx/e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 289.21, "height": 74.56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "36KMxufilLpaPU3RWZ7qlb"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "PLAY NOW", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 56, "_fontSize": 56, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 56, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "911a9cbd-c991-48bf-821d-c52a2857ad6c", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_id": "9fIqc5BtFIgaZAzh1EONcE"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 97, "b": 19, "a": 255}, "_width": 2, "_id": "ceBkkYvP1KB5yl+FVRHJvh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 212, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f4+dv3PuJHtKjRTiWf3fMD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "27674c0c-1372-4c62-adb0-7e0989e06249@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "82FzvH6VFJSIMZFAsQC3iT"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "__prefab": null, "_id": "7ftRmItN1B0IY0RRBeyAwj"}, {"__type__": "e043a38/uhJm4d+KXaHDTru", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "__prefab": null, "ctaNode": {"__id__": 199}, "scaleAmount": 1.1, "duration": 0.5, "useBackEasing": true, "_id": "28jZRg3bhEuqpwD2qgj0g+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 198}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 212, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9dIGMyILZNWIKF3fIENdC8"}, {"__type__": "e043a38/uhJm4d+KXaHDTru", "_name": "", "_objFlags": 0, "node": {"__id__": 198}, "_enabled": true, "__prefab": null, "ctaNode": {"__id__": 199}, "scaleAmount": 1.1, "duration": 0.5, "useBackEasing": true, "_id": "d3HsRQpCFCbJmkNTXKAPm0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fb4ZkqC5JE7aIr+bN8YzH+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1, "_originalHeight": 1, "_alignMode": 2, "_lockFlags": 0, "_id": "8fTFtr305DWqTIlTc9STtN"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "789+MaDAVFj5RJ/biHL9Uk"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1080, "_originalHeight": 1010, "_alignMode": 2, "_lockFlags": 45, "_id": "02PpgrF6NNIZOyJ2bxlD3M"}, {"__type__": "cc.Node", "_name": "PopupCollection", "_objFlags": 0, "_parent": {"__id__": 189}, "_children": [{"__id__": 215}, {"__id__": 219}, {"__id__": 223}, {"__id__": 246}], "_active": false, "_components": [{"__id__": 250}, {"__id__": 251}, {"__id__": 252}, {"__id__": 253}, {"__id__": 254}, {"__id__": 255}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b81zl9O1RKBbHHdazmUeA9"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 214}, "_children": [], "_active": true, "_components": [{"__id__": 216}, {"__id__": 217}, {"__id__": 218}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "414fauxA9JIJvdqLN1nNAi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "43WvVfxbFIA6UCD+ILxYs1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 200}, "_spriteFrame": {"__uuid__": "7321f17a-7536-48e1-82cb-fc49fe63e0d2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "75beZJK4dM7r/4r8cKK4ca"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1, "_originalHeight": 1, "_alignMode": 2, "_lockFlags": 0, "_id": "15SAUm6thPUY6ubePMXmgY"}, {"__type__": "cc.Node", "_name": "Label-002", "_objFlags": 0, "_parent": {"__id__": 214}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 221}, {"__id__": 222}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 573.105, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fdNKEMDOlDzIhzuw/jbCGv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 219}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 612, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "22w1RhS9FIp4UCi6WBNIoL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 219}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "You found", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 80, "_fontSize": 80, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "911a9cbd-c991-48bf-821d-c52a2857ad6c", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "aa9pYddw9LCZHufrgLCIlu"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 219}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 1, "_id": "27dLDXWxtB7qQr3qLzbg5X"}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "_parent": {"__id__": 214}, "_children": [{"__id__": 224}, {"__id__": 230}], "_active": true, "_components": [{"__id__": 245}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "15ypX0Ag5BGaOh8QLtvKLD"}, {"__type__": "cc.Node", "_name": "spr_cardbg_Item_v3_InOut", "_objFlags": 0, "_parent": {"__id__": 223}, "_children": [{"__id__": 225}], "_active": true, "_components": [{"__id__": 228}, {"__id__": 229}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 46.***************, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3bTib5Ue9I465OK1lx9quS"}, {"__type__": "cc.Node", "_name": "Tung-Tung-Tung-Sahur-Brainrot-Trend", "_objFlags": 0, "_parent": {"__id__": 224}, "_children": [], "_active": true, "_components": [{"__id__": 226}, {"__id__": 227}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 25.289, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.2, "y": 1.2, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d8zC1LLZBPgrnd8Gi2lnXY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 206, "height": 485}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "20YOP1yj1OrYnSD7wJ7qnx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4b1cbb78-f235-4b94-ad5d-cdc78ff4579a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "82PphqWzNHe5AaMp+TwFLM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 601.401, "height": 802.5699999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "250/gA0GJHbZsH4K9Or2Zs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 224}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "32394ea0-5e06-4ad8-a539-8fde2d04ed2a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "18iIBJMBlBT5fLjLQ5L88Z"}, {"__type__": "cc.Node", "_name": "sprTileRedInOut", "_objFlags": 0, "_parent": {"__id__": 223}, "_children": [{"__id__": 231}, {"__id__": 235}, {"__id__": 239}], "_active": true, "_components": [{"__id__": 243}, {"__id__": 244}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -354.86699999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "felBtRp6tJ06hMtB1o3EUc"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [], "_active": false, "_components": [{"__id__": 232}, {"__id__": 233}, {"__id__": 234}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 56.131, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1fsq9Dt+xFf4UNjA2l+aKf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 612, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "39QVgto7lC45+nblc+qUXM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 224, "b": 0, "a": 255}, "_string": "Collection: <PERSON><PERSON>", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "911a9cbd-c991-48bf-821d-c52a2857ad6c", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "1cdohwkBVIRZjJ52ZxTj8I"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 1, "_id": "e3kAF2D+hCzIcM8PuPCqx1"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [], "_active": false, "_components": [{"__id__": 236}, {"__id__": 237}, {"__id__": 238}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 56.131, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "99oDQF0UhC+7nArGIhwR2b"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 235}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 612, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "12bOut2Q9JSJaXwJLp0Erf"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 235}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Collection:", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "911a9cbd-c991-48bf-821d-c52a2857ad6c", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "9eDYBs81NOf6WPwtbq+jTv"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 235}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 1, "_id": "16Yp6T5M9B87DM0t3H1PG6"}, {"__type__": "cc.Node", "_name": "Label-002", "_objFlags": 0, "_parent": {"__id__": 230}, "_children": [], "_active": true, "_components": [{"__id__": 240}, {"__id__": 241}, {"__id__": 242}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 56.131, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e1vmH5x6hP9qfBIGorUMVk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 612, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "01kMtfoPpNP5Z1vubqz994"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Collection: <PERSON><PERSON>", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "911a9cbd-c991-48bf-821d-c52a2857ad6c", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "0aw/VJOaxGEa5wkJNxIw8V"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 1, "_id": "ebo+IMR51MC4xglQ+7uQ20"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 967.264, "height": 240}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bdyJvyMYFOar64VU+bmsbx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f87aa3fd-2bed-4320-b1b2-f8ac89cb1552@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f9OHP9A0hCSaTOgvcW6hzr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d8SUb056ZChYexziykvAYE"}, {"__type__": "cc.Node", "_name": "Label-003", "_objFlags": 0, "_parent": {"__id__": 214}, "_children": [], "_active": true, "_components": [{"__id__": 247}, {"__id__": 248}, {"__id__": 249}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -550.289, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d7UB5zh+BH/5kbisjvvaJT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 612, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c1NeKtQAlPt7ur7De7KA6m"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Tap to claim", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "911a9cbd-c991-48bf-821d-c52a2857ad6c", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "f3dg8qTUtNe5OpD++7s3Ly"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 1, "_id": "20661tVWlLOrg2FeZZJRil"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 214}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7bqa19iGJHiqzKAMWuB9FP"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 214}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1, "_originalHeight": 1, "_alignMode": 2, "_lockFlags": 0, "_id": "2eQTM+yZBLJKX3jk9SKrid"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 214}, "_enabled": true, "__prefab": null, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "b8wgvokMRLBKNzyUISIUHG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 214}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1080, "_originalHeight": 1010, "_alignMode": 2, "_lockFlags": 45, "_id": "3b5kn7dTJK5YR4yhvMYPJ6"}, {"__type__": "ea72akiL11JrZTpY2QEMD40", "_name": "", "_objFlags": 0, "node": {"__id__": 214}, "_enabled": true, "__prefab": null, "cardNode": {"__id__": 223}, "uiOpacity": {"__id__": 255}, "_id": "2eVk40FbBFjJIM/9XuxCyd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 214}, "_enabled": true, "__prefab": null, "_opacity": 255, "_id": "0b2OkrukhOPoWjai2KHwab"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 189}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "70Nztdo4dBI5C4Lib+I1Wb"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 189}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 45, "_id": "7adLyvgs1ADrnTi5tytG7+"}, {"__type__": "b3c63x+52dA1ajjoS/B9ndS", "_name": "", "_objFlags": 0, "node": {"__id__": 189}, "_enabled": true, "__prefab": null, "popupLose": {"__id__": 190}, "popupWin": {"__id__": 190}, "popupCollection": {"__id__": 214}, "ctaButton": {"__id__": 206}, "_id": "9dKX//fb5C9pnt0t7DwYIM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ca9IhQdotDMahJIn3d754I"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 45, "_id": "b5HruKb0RPSobG7eHiOkwx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 10}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 5.684341886080802e-14, "_bottom": 5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "92fbdbc7-20aa-403b-bfe3-e6447a8b8270", "instance": null, "targetOverrides": [{"__id__": 265}, {"__id__": 267}], "nestedPrefabInstanceRoots": [{"__id__": 101}, {"__id__": 112}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 121}, "sourceInfo": null, "propertyPath": ["particle1"], "target": {"__id__": 101}, "targetInfo": {"__id__": 266}}, {"__type__": "cc.TargetInfo", "localID": ["ccxu7OtEhFl7ltK4t+NlDo"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 121}, "sourceInfo": null, "propertyPath": ["particle2"], "target": {"__id__": 112}, "targetInfo": {"__id__": 268}}, {"__type__": "cc.TargetInfo", "localID": ["ccxu7OtEhFl7ltK4t+NlDo"]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 270}, "shadows": {"__id__": 271}, "_skybox": {"__id__": 272}, "fog": {"__id__": 273}, "octree": {"__id__": 274}, "lightProbeInfo": {"__id__": 275}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null}]