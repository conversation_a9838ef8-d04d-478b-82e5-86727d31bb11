import { _decorator, AudioClip, AudioSource, Component, <PERSON><PERSON>, director, tween } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('SoundManager')
export class SoundManager extends Component {
    private static _instance: SoundManager | null = null;

    public static get Instance(): SoundManager {
        if (!this._instance) {
            console.error("SoundManager instance is not yet available.");
        }
        return this._instance!;
    }

    protected onLoad(): void {
        SoundManager._instance = this;
        director.addPersistRootNode(this.node);

        this._audioSource = this.node.addComponent(AudioSource);
        this._bgmSource = this.node.addComponent(AudioSource);
        this._bgmSource.loop = true;
    }

    @property(AudioClip)
    BGM: AudioClip = null;

    @property(AudioClip)
    Button_Click: AudioClip = null;

    @property(AudioClip)
    Win: AudioClip = null;

    @property(AudioClip)
    Lose: AudioClip = null;

    @property(AudioClip)
    Match: AudioClip = null;

    @property(AudioClip)
    Popup: AudioClip = null;

    private _audioSource: AudioSource = null;
    private _bgmSource: AudioSource = null;


    public playSfx(sfxClips: AudioClip, volume: number = 1.0) {
        this._audioSource.clip = sfxClips;
        this._audioSource.volume = volume;
        this._audioSource.play();
    }

    public playBgm(bgmClips: AudioClip, volume: number = 1.0) {
        this._bgmSource.clip = bgmClips;
        this._bgmSource.volume = .4;
        this._bgmSource.play();
    }

    public stopBgm(fadeDuration: number = 1.0) {
        if (!this._bgmSource || !this._bgmSource.playing) {
            return;
        }

        const currentVolume = this._bgmSource.volume;

        tween(this._bgmSource)
            .to(fadeDuration, { volume: 0 })
            .call(() => {
                this._bgmSource.stop();
                this._bgmSource.volume = currentVolume; // Restore volume for next play
                console.log('SoundManager: BGM stopped with fade out');
            })
            .start();
    }

    public pauseBgm() {
        this._bgmSource.pause();
    }

}


window["gameClose"] = function () {
    SoundManager.Instance.stopBgm();
};