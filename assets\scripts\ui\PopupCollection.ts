import { _decorator, Component, Node, Vec3, tween, UIOpacity } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('PopupCollection')
export class PopupCollection extends Component {
    @property(Node) cardNode: Node = null!;
    @property(UIOpacity) uiOpacity: UIOpacity = null!;

    start() {
        this.playAnimation();
    }

    playAnimation() {
        // Set scale ban đầu về 0
        this.node.scale = Vec3.ZERO;

        // Animation scale từ 0 đến 1
        tween(this.node)
            .to(0.3, { scale: Vec3.ONE }, { easing: 'backOut' })
            .start();
        this.cardNode.active = true;
    }

    public hide() {
        tween(this.uiOpacity)
            .to(0.3, { opacity: 0 })
            .call(() => {
                this.node.active = false;
            })
            .start();
    }
}


