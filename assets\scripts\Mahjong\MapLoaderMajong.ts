import { _decorator, CCInteger, Component, Node, Prefab } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('MapLoaderMajong')
export class MapLoaderMajong extends Component {
    @property(Prefab) private tilePrefab: Prefab = null!;
    @property([CCInteger]) private arrType: number[] = [];
    @property(Node) private container: Node = null!;

    onLoad() {
        // this.loadMap();
    }

    private loadMap() {
        for (let i = 0; i < this.arrType.length; i++) {
            const type = this.arrType[i];
            this.spawnTile(type);
        }
    }

    private spawnTile(tileType: number) {
        // const tileNode = instantiate(this.tilePrefab);
        // tileNode.parent = this.node;

        // const tileComponent = tileNode.getComponent(TileMahjong);
        // if (tileComponent) {
        //     tileComponent.tileType = tileType;
        // }   
    }

}


