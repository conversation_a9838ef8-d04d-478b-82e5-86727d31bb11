import { _decorator, AudioClip, AudioSource, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('SoundAdapter')
export class SoundAdapter extends Component {
    @property(AudioClip) public music: AudioClip = null!;
    @property(AudioClip) public click: AudioClip = null!;
    @property(AudioClip) public match: AudioClip = null!;
    @property(AudioClip) public bubble_pop: AudioClip = null!;

    @property(AudioSource) private musicSource: AudioSource = null!;
    @property(AudioSource) private sfxSource: AudioSource = null!;

    public static instance: SoundAdapter = null!;

    protected onLoad(): void {
        SoundAdapter.instance = this;
    }

    public playMusic() {
        this.musicSource.clip = this.music;
        this.musicSource.play();
    }

    public stopMusic() {
        this.musicSource.stop();
    }

    public playClick() {
        this.sfxSource.clip = this.click;
        this.sfxSource.play();
    }

    public playMatch() {
        this.sfxSource.clip = this.match;
        this.sfxSource.play();
    }

    public playBubblePop() {
        this.sfxSource.clip = this.bubble_pop;
        this.sfxSource.play();
    }
}


