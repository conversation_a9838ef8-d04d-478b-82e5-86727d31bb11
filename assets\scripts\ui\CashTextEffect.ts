import { _decorator, CCInteger, Component, Label, Node, tween, Vec3 } from 'cc';
import { ProgressBarUI } from './ProgressBarUI';
const { ccclass, property } = _decorator;

@ccclass('CashTextEffect')
export class CashTextEffect extends Component {
    @property(Label) lblCash: Label = null!;
    @property(CCInteger) value: Number = 0;

    @property(ProgressBarUI)
    progressBar: ProgressBarUI = null!;

    private currentValue: number = 0;
    private isAnimating: boolean = false;

    protected onLoad(): void {
        this.progressBar.onChangeValue.on(this.onProgressChanged, this);
    }

    private onProgressChanged(value: number) {
        this.updateCashText(value * 30);
    }

    public updateCashText(value: number) {
        // Nếu giá trị tăng, chạy hiệu ứng
        if (value > this.currentValue) {
            this.playIncreaseEffect(value);
        } else {
            // Nếu giá trị giảm hoặc bằng, cập nhật trực tiếp
            this.lblCash.string = '$' + value;
            this.currentValue = value;
        }
    }

    /**
     * Hiệu ứng khi tăng tiền
     */
    private playIncreaseEffect(newValue: number) {
        if (this.isAnimating) {
            return; // Tránh chạy nhiều animation cùng lúc
        }

        this.isAnimating = true;
        const oldValue = this.currentValue;
        const difference = newValue - oldValue;

        // Animation 1: Scale effect (phóng to rồi thu nhỏ)
        const originalScale = this.lblCash.node.scale.clone();
        tween(this.lblCash.node)
            .to(0.1, { scale: new Vec3(1.2, 1.2, 1) }, { easing: 'backOut' })
            .to(0.1, { scale: originalScale }, { easing: 'backIn' })
            .start();



        // Animation 2: Counter effect (đếm từ giá trị cũ lên giá trị mới)
        tween({ value: oldValue })
            .to(0.5, { value: newValue }, {
                easing: 'sineOut',
                onUpdate: (target: any) => {
                    const currentDisplayValue = Math.floor(target.value);
                    this.lblCash.string = '$' + currentDisplayValue;
                },
                onComplete: () => {
                    this.lblCash.string = '$' + newValue;
                    this.currentValue = newValue;
                    this.isAnimating = false;
                }
            })
            .start();

        // Hiệu ứng bonus: Hiển thị số tiền tăng thêm
        this.showBonusText(difference);
    }

    /**
     * Hiển thị text bonus (+$xxx) bay lên
     */
    private showBonusText(amount: number) {
        // Tạo node tạm để hiển thị bonus text
        const bonusNode = new Node('BonusText');
        const bonusLabel = bonusNode.addComponent(Label);

        bonusLabel.string = '+$' + amount;
        bonusLabel.fontSize = this.lblCash.fontSize * 0.8;

        // Sử dụng cùng font với lblCash
        bonusLabel.font = this.lblCash.font;
        // Để màu trắng mặc định, không set màu

        // Thêm vào parent của lblCash
        bonusNode.parent = this.lblCash.node.parent;
        bonusNode.position = this.lblCash.node.position.clone();

        // Animation bay lên
        tween(bonusNode)
            .to(1.0, {
                position: new Vec3(bonusNode.position.x, bonusNode.position.y + 50, 0),
                scale: new Vec3(0.5, 0.5, 1)
            })
            .call(() => {
                bonusNode.destroy(); // Xóa node sau khi animation xong
            })
            .start();
    }

    /**
     * Set giá trị ban đầu mà không có hiệu ứng
     */
    public setInitialValue(value: number) {
        this.currentValue = value;
        this.lblCash.string = '$' + value;
    }
}


