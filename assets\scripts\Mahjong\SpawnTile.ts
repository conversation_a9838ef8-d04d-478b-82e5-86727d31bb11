import { _decorator, Component, Prefab, instantiate, Vec3, CCInteger, CCFloat, Node, UITransform } from 'cc';
import { TileMahjong } from './TileMahjong';
const { ccclass, property } = _decorator;

@ccclass('SpawnTile')
export class SpawnTile extends Component {

    @property(Prefab) private tilePrefab: Prefab = null!;

    @property(CCFloat) private delay = 1.0;
    @property(CCInteger) private tileType = 0;
    @property(CCInteger) private count = 100;
    @property({ tooltip: "Có random vị trí Y hay không (nếu false thì spawn ở đáy node)" })
    private randomY: boolean = false;

    @property(Node) private targetPos: Node = null!;


    protected start(): void {
        this.startSpawn();
    }

    public spawnTile(tileType: number, position?: Vec3) {
        const tileNode = instantiate(this.tilePrefab);
        tileNode.parent = this.node;

        // Nếu không có position được truyền vào, tạo random position
        if (!position) {
            position = this.getRandomSpawnPosition();
        }

        tileNode.position = position;
        const tileComponent = tileNode.getComponent(TileMahjong);
        if (tileComponent) {
            tileComponent.tileType = tileType;
        }
        return tileComponent;
    }

    /**
     * Tạo vị trí spawn random trong khoảng UITransform của node hiện tại
     */
    private getRandomSpawnPosition(): Vec3 {
        const uiTransform = this.node.getComponent(UITransform);
        if (!uiTransform) {
            console.warn("SpawnTile: Node không có UITransform component, sử dụng vị trí (0,0,0)");
            return Vec3.ZERO.clone();
        }

        // Lấy kích thước của node
        const width = uiTransform.width;
        const height = uiTransform.height;

        // Tính toán anchor để xác định vùng spawn
        const anchorX = uiTransform.anchorX;
        const anchorY = uiTransform.anchorY;

        // Tính toán vùng spawn dựa trên anchor
        const minX = -width * anchorX;
        const maxX = width * (1 - anchorX);
        const minY = -height * anchorY;
        const maxY = height * (1 - anchorY);

        // Random vị trí x trong khoảng [minX, maxX]
        const randomX = minX + Math.random() * (maxX - minX);

        // Có thể giữ nguyên Y hoặc cũng random Y, tùy theo yêu cầu
        // Ở đây tôi sẽ spawn ở đáy của node (minY)
        const spawnY = minY;

        const randomPosition = new Vec3(randomX, spawnY, 0);

        console.log(`SpawnTile: Random spawn position: (${randomX.toFixed(2)}, ${spawnY.toFixed(2)}) within bounds [${minX.toFixed(2)}, ${maxX.toFixed(2)}]`);

        return randomPosition;
    }

    private isSpawning: boolean = false;
    private activeBubbles: TileMahjong[] = []; // Theo dõi các bubble đang di chuyển
    private readonly MAX_CONCURRENT_BUBBLES = 3; // Tối đa 3 bubble cùng lúc

    /**
     * Bắt đầu quá trình spawn tiles
     */
    public startSpawn() {
        if (this.isSpawning) {
            console.warn("SpawnTile: Already spawning!");
            return;
        }

        if (this.count <= 0) {
            console.warn("SpawnTile: Count must be greater than 0");
            return;
        }

        if (!this.tilePrefab) {
            console.error("SpawnTile: Tile prefab is not assigned!");
            return;
        }

        this.isSpawning = true;
        this.spawnLoop();
    }

    /**
     * Dừng quá trình spawn
     */
    public stopSpawn() {
        this.isSpawning = false;
        // Dọn dẹp danh sách bubble đang hoạt động
        this.activeBubbles = [];
        console.log("SpawnTile: Spawn stopped and active bubbles cleared");
    }

    /**
     * Vòng lặp spawn tiles - tối đa 3 bubble cùng lúc
     */
    private async spawnLoop() {
        while (this.isSpawning && this.count > 0) {
            // Kiểm tra và dọn dẹp các bubble đã hoàn thành
            this.cleanupCompletedBubbles();

            // Nếu đã đủ 3 bubble thì đợi
            if (this.activeBubbles.length >= this.MAX_CONCURRENT_BUBBLES) {
                await this.waitForDelay(0.1); // Đợi 100ms rồi kiểm tra lại
                continue;
            }

            // Random tile type từ 0 đến this.tileType
            const randomTileType = Math.floor(Math.random() * (this.tileType + 1));

            // Spawn tile tại vị trí random trong khoảng UITransform
            const spawnedTile = this.spawnTile(randomTileType);

            if (spawnedTile) {
                // Thêm vào danh sách bubble đang hoạt động
                this.activeBubbles.push(spawnedTile);

                // Di chuyển tile đến vị trí đích (không await - chạy song song)
                this.moveBubbleAsync(spawnedTile, randomTileType);

                console.log(`SpawnTile: Spawned tile type ${randomTileType}, active bubbles: ${this.activeBubbles.length}, remaining count: ${this.count - 1}`);
            }

            // Giảm count
            this.count--;

            // Đợi delay trước khi spawn bubble tiếp theo
            if (this.count > 0 && this.isSpawning) {
                await this.waitForDelay(this.delay);
            }
        }

        // Đợi tất cả bubble hoàn thành trước khi kết thúc
        while (this.activeBubbles.length > 0) {
            this.cleanupCompletedBubbles();
            await this.waitForDelay(0.1);
        }

        // Kết thúc spawn
        this.isSpawning = false;
        console.log("SpawnTile: Spawn completed!");
    }

    /**
     * Di chuyển bubble một cách bất đồng bộ
     */
    private async moveBubbleAsync(bubble: TileMahjong, tileType: number) {
        try {
            await bubble.startMove(this.targetPos.getWorldPosition().clone());
            console.log(`SpawnTile: Bubble type ${tileType} completed movement`);
        } catch (error) {
            console.error(`SpawnTile: Error moving bubble type ${tileType}:`, error);
        }
    }

    /**
     * Dọn dẹp các bubble đã hoàn thành di chuyển
     */
    private cleanupCompletedBubbles() {
        this.activeBubbles = this.activeBubbles.filter(bubble => {
            // Kiểm tra xem bubble còn đang di chuyển không
            if (!bubble || !bubble.isMoving) {
                return false; // Loại bỏ khỏi danh sách
            }
            return true; // Giữ lại trong danh sách
        });
    }

    /**
     * Hàm đợi delay
     */
    private waitForDelay(delayTime: number): Promise<void> {
        return new Promise((resolve) => {
            this.scheduleOnce(() => {
                resolve();
            }, delayTime);
        });
    }

    /**
     * Reset count để có thể spawn lại
     */
    public resetCount(newCount: number) {
        this.count = newCount;
    }

    /**
     * Kiểm tra xem có đang spawn không
     */
    public get isCurrentlySpawning(): boolean {
        return this.isSpawning;
    }
}


