import { _decorator, Component, Prefab, instantiate, Vec3, CCInteger, CCFloat, Node } from 'cc';
import { TileMahjong } from './TileMahjong';
const { ccclass, property } = _decorator;

@ccclass('SpawnTile')
export class SpawnTile extends Component {

    @property(Prefab) private tilePrefab: Prefab = null!;

    @property(CCFloat) private delay = 1.0;
    @property(CCInteger) private tileType = 0;
    @property(CCInteger) private count = 100;

    @property(Node) private targetPos: Node = null!;


    protected start(): void {
        this.startSpawn();
    }

    public spawnTile(tileType: number, position: Vec3) {
        const tileNode = instantiate(this.tilePrefab);
        tileNode.parent = this.node;
        tileNode.position = position;
        const tileComponent = tileNode.getComponent(TileMahjong);
        if (tileComponent) {
            tileComponent.tileType = tileType;
        }
        return tileComponent;
    }

    private isSpawning: boolean = false;

    /**
     * <PERSON><PERSON>t đầu quá trình spawn tiles
     */
    public startSpawn() {
        if (this.isSpawning) {
            console.warn("SpawnTile: Already spawning!");
            return;
        }

        if (this.count <= 0) {
            console.warn("SpawnTile: Count must be greater than 0");
            return;
        }

        if (!this.tilePrefab) {
            console.error("SpawnTile: Tile prefab is not assigned!");
            return;
        }

        this.isSpawning = true;
        this.spawnLoop();
    }

    /**
     * Dừng quá trình spawn
     */
    public stopSpawn() {
        this.isSpawning = false;
    }

    /**
     * Vòng lặp spawn tiles
     */
    private async spawnLoop() {
        while (this.isSpawning && this.count > 0) {
            // Random tile type từ 0 đến this.tileType
            const randomTileType = Math.floor(Math.random() * (this.tileType + 1));

            // Spawn tile tại vị trí hiện tại của SpawnTile
            const spawnedTile = this.spawnTile(randomTileType, Vec3.ZERO);
            await spawnedTile.startFloatBezier(this.targetPos.getWorldPosition().clone());

            if (spawnedTile) {
                console.log(`SpawnTile: Spawned tile type ${randomTileType}, remaining count: ${this.count - 1}`);
            }

            // Giảm count
            this.count--;

            // Nếu còn count thì đợi delay rồi tiếp tục
            if (this.count > 0 && this.isSpawning) {
                await this.waitForDelay(this.delay);
            }
        }

        // Kết thúc spawn
        this.isSpawning = false;
        console.log("SpawnTile: Spawn completed!");
    }

    /**
     * Hàm đợi delay
     */
    private waitForDelay(delayTime: number): Promise<void> {
        return new Promise((resolve) => {
            this.scheduleOnce(() => {
                resolve();
            }, delayTime);
        });
    }

    /**
     * Reset count để có thể spawn lại
     */
    public resetCount(newCount: number) {
        this.count = newCount;
    }

    /**
     * Kiểm tra xem có đang spawn không
     */
    public get isCurrentlySpawning(): boolean {
        return this.isSpawning;
    }
}


