import { _decorator, Component, Prefab, instantiate, Vec3, CCInteger, CCFloat, Node, UITransform } from 'cc';
import { TileMahjong } from './TileMahjong';
import { GameControl } from './GameControl';
const { ccclass, property } = _decorator;

@ccclass('SpawnTile')
export class SpawnTile extends Component {

    @property(Prefab) private tilePrefab: Prefab = null!;

    @property(CCFloat) private delay = 5.0;
    @property(CCInteger) private tileType = 0;
    @property(CCInteger) private count = 100;
    @property({ tooltip: "Có random vị trí Y hay không (nếu false thì spawn ở đáy node)" })
    private randomY: boolean = false;
    @property({ tooltip: "Có random target X hay không (nếu false thì dùng vị trí cố định của targetPos)" })
    private randomTargetX: boolean = false;
    @property({ tooltip: "Có random target Y hay không (nếu false thì dùng vị trí cố định của targetPos)" })
    private randomTargetY: boolean = false;

    @property(Node) private spawnZone: Node[] = [];

    @property(Node) private targetPos: Node = null!;

    public spawnTile(tileType: number, position?: Vec3) {
        const tileNode = instantiate(this.tilePrefab);
        tileNode.parent = this.node;

        // Nếu không có position được truyền vào, tạo random position
        if (!position) {
            position = this.getRandomSpawnPosition();
        }

        tileNode.position = position;
        const tileComponent = tileNode.getComponent(TileMahjong);
        if (tileComponent) {
            tileComponent.tileType = tileType;
            
            GameControl.instance.arrTile.push(tileComponent);

        }
        return tileComponent;
    }

    /**
     * Tạo vị trí spawn random trong khoảng UITransform của node ngẫu nhiên từ spawnZone
     * Nếu spawnZone trống, sử dụng node hiện tại
     */
    private getRandomSpawnPosition(): Vec3 {
        let targetNode: Node;

        // Nếu có spawnZone, chọn ngẫu nhiên một node từ mảng
        if (this.spawnZone && this.spawnZone.length > 0) {
            const randomIndex = Math.floor(Math.random() * this.spawnZone.length);
            targetNode = this.spawnZone[randomIndex];
            console.log(`SpawnTile: Selected spawn zone ${randomIndex} (${targetNode.name})`);
        } else {
            // Fallback về node hiện tại nếu không có spawnZone
            targetNode = this.node;
            console.log("SpawnTile: Using current node as spawn zone (no spawnZone configured)");
        }

        const uiTransform = targetNode.getComponent(UITransform);
        if (!uiTransform) {
            console.warn(`SpawnTile: Node ${targetNode.name} không có UITransform component, sử dụng vị trí (0,0,0)`);
            return Vec3.ZERO.clone();
        }

        // Lấy kích thước của node
        const width = uiTransform.width;
        const height = uiTransform.height;

        // Tính toán anchor để xác định vùng spawn
        const anchorX = uiTransform.anchorX;
        const anchorY = uiTransform.anchorY;

        // Tính toán vùng spawn dựa trên anchor
        const minX = -width * anchorX;
        const maxX = width * (1 - anchorX);
        const minY = -height * anchorY;
        const maxY = height * (1 - anchorY);

        // Random vị trí x trong khoảng [minX, maxX]
        const randomX = minX + Math.random() * (maxX - minX);

        // Random vị trí Y nếu được bật, nếu không thì spawn ở đáy node
        const spawnY = this.randomY
            ? minY + Math.random() * (maxY - minY)  // Random Y trong toàn bộ chiều cao
            : minY;  // Spawn ở đáy node

        // Tạo vị trí local trong targetNode
        const localPosition = new Vec3(randomX, spawnY, 0);

        // Chuyển đổi từ local position của targetNode sang world position
        const worldPosition = targetNode.getComponent(UITransform)!.convertToWorldSpaceAR(localPosition);

        // Chuyển đổi từ world position sang local position của node hiện tại (parent của tile)
        const finalPosition = this.node.getComponent(UITransform)!.convertToNodeSpaceAR(worldPosition);

        console.log(`SpawnTile: Random spawn position in ${targetNode.name}: local(${randomX.toFixed(2)}, ${spawnY.toFixed(2)}) → final(${finalPosition.x.toFixed(2)}, ${finalPosition.y.toFixed(2)})`);

        return finalPosition;
    }

    /**
     * Tạo vị trí target random dựa trên UITransform của targetPos node
     */
    private getRandomTargetPosition(): Vec3 {
        if (!this.targetPos) {
            console.warn("SpawnTile: targetPos node không được gán, sử dụng vị trí (0,0,0)");
            return Vec3.ZERO.clone();
        }

        // Lấy world position gốc của target
        const baseTargetWorldPos = this.targetPos.getWorldPosition().clone();

        // Nếu không random gì cả, trả về vị trí gốc
        if (!this.randomTargetX && !this.randomTargetY) {
            return baseTargetWorldPos;
        }

        const targetUITransform = this.targetPos.getComponent(UITransform);
        if (!targetUITransform) {
            console.warn("SpawnTile: targetPos node không có UITransform component, sử dụng vị trí gốc");
            return baseTargetWorldPos;
        }

        // Tính toán vùng target dựa trên UITransform và anchor của targetPos
        const width = targetUITransform.width;
        const height = targetUITransform.height;
        const anchorX = targetUITransform.anchorX;
        const anchorY = targetUITransform.anchorY;

        // Tính toán offset từ center của targetPos
        const minX = -width * anchorX;
        const maxX = width * (1 - anchorX);
        const minY = -height * anchorY;
        const maxY = height * (1 - anchorY);

        // Random offset X nếu được bật
        const offsetX = this.randomTargetX
            ? minX + Math.random() * (maxX - minX)
            : 0;

        // Random offset Y nếu được bật
        const offsetY = this.randomTargetY
            ? minY + Math.random() * (maxY - minY)
            : 0;

        // Tính toán world position cuối cùng
        const finalTargetWorldPos = new Vec3(
            baseTargetWorldPos.x + offsetX,
            baseTargetWorldPos.y + offsetY,
            baseTargetWorldPos.z
        );

        console.log(`SpawnTile: Random target position: (${finalTargetWorldPos.x.toFixed(2)}, ${finalTargetWorldPos.y.toFixed(2)}) with offset (${offsetX.toFixed(2)}, ${offsetY.toFixed(2)})`);

        return finalTargetWorldPos;
    }

    private isSpawning: boolean = false;
    private activeBubbles: TileMahjong[] = []; // Theo dõi các bubble đang di chuyển
    private readonly MAX_CONCURRENT_BUBBLES = 5; // Tối đa 3 bubble cùng lúc

    /**
     * Bắt đầu quá trình spawn tiles
     */
    public startSpawn() {
        if (this.isSpawning) {
            console.warn("SpawnTile: Already spawning!");
            return;
        }

        if (this.count <= 0) {
            console.warn("SpawnTile: Count must be greater than 0");
            return;
        }

        if (!this.tilePrefab) {
            console.error("SpawnTile: Tile prefab is not assigned!");
            return;
        }

        // Validate spawn zones trước khi bắt đầu
        if (this.spawnZone.length > 0) {
            this.validateSpawnZones();
            console.log(this.getSpawnZoneDebugInfo());
        }

        this.isSpawning = true;
        this.spawnLoop();
    }

    /**
     * Dừng quá trình spawn
     */
    public stopSpawn() {
        this.isSpawning = false;
        // Dọn dẹp danh sách bubble đang hoạt động
        this.activeBubbles = [];
        console.log("SpawnTile: Spawn stopped and active bubbles cleared");
    }

    /**
     * Vòng lặp spawn tiles - tối đa 3 bubble cùng lúc
     */
    private async spawnLoop() {
        while (this.isSpawning && this.count > 0) {
            // Kiểm tra và dọn dẹp các bubble đã hoàn thành
            this.cleanupCompletedBubbles();

            // Nếu đã đủ 3 bubble thì đợi
            if (this.activeBubbles.length >= this.MAX_CONCURRENT_BUBBLES) {
                await this.waitForDelay(0.1); // Đợi 100ms rồi kiểm tra lại
                continue;
            }

            // Random tile type từ 0 đến this.tileType
            const randomTileType = Math.floor(Math.random() * (this.tileType + 1));

            // Spawn tile tại vị trí random trong khoảng UITransform
            const spawnedTile = this.spawnTile(randomTileType);

            if (spawnedTile) {
                // Thêm vào danh sách bubble đang hoạt động
                this.activeBubbles.push(spawnedTile);

                // Di chuyển tile đến vị trí đích (không await - chạy song song)
                this.moveBubbleAsync(spawnedTile, randomTileType);

                console.log(`SpawnTile: Spawned tile type ${randomTileType}, active bubbles: ${this.activeBubbles.length}, remaining count: ${this.count - 1}`);
            }

            // Giảm count
            this.count--;

            // Đợi delay trước khi spawn bubble tiếp theo
            if (this.count > 0 && this.isSpawning) {
                await this.waitForDelay(this.delay);
            }
        }

        // Đợi tất cả bubble hoàn thành trước khi kết thúc
        while (this.activeBubbles.length > 0) {
            this.cleanupCompletedBubbles();
            await this.waitForDelay(0.1);
        }

        // Kết thúc spawn
        this.isSpawning = false;
        console.log("SpawnTile: Spawn completed!");
    }

    /**
     * Di chuyển bubble một cách bất đồng bộ
     */
    private async moveBubbleAsync(bubble: TileMahjong, tileType: number) {
        try {
            // Sử dụng random target position thay vì vị trí cố định
            const targetPosition = this.getRandomTargetPosition();
            await bubble.startMove(targetPosition);
            console.log(`SpawnTile: Bubble type ${tileType} completed movement`);
        } catch (error) {
            console.error(`SpawnTile: Error moving bubble type ${tileType}:`, error);
        }
    }

    /**
     * Dọn dẹp các bubble đã hoàn thành di chuyển
     */
    private cleanupCompletedBubbles() {
        this.activeBubbles = this.activeBubbles.filter(bubble => {
            // Kiểm tra xem bubble còn đang di chuyển không
            if (!bubble || !bubble.isMoving) {
                return false; // Loại bỏ khỏi danh sách
            }
            return true; // Giữ lại trong danh sách
        });
    }

    /**
     * Hàm đợi delay
     */
    private waitForDelay(delayTime: number): Promise<void> {
        return new Promise((resolve) => {
            this.scheduleOnce(() => {
                resolve();
            }, delayTime);
        });
    }

    /**
     * Reset count để có thể spawn lại
     */
    public resetCount(newCount: number) {
        this.count = newCount;
    }

    /**
     * Kiểm tra xem có đang spawn không
     */
    public get isCurrentlySpawning(): boolean {
        return this.isSpawning;
    }

    /**
     * Thêm spawn zone mới vào mảng
     */
    public addSpawnZone(node: Node): void {
        if (!node) {
            console.warn("SpawnTile: Cannot add null node to spawn zone");
            return;
        }

        if (!node.getComponent(UITransform)) {
            console.warn(`SpawnTile: Node ${node.name} không có UITransform component, không thể thêm vào spawn zone`);
            return;
        }

        if (this.spawnZone.indexOf(node) === -1) {
            this.spawnZone.push(node);
            console.log(`SpawnTile: Added ${node.name} to spawn zone. Total zones: ${this.spawnZone.length}`);
        } else {
            console.warn(`SpawnTile: Node ${node.name} đã có trong spawn zone`);
        }
    }

    /**
     * Xóa spawn zone khỏi mảng
     */
    public removeSpawnZone(node: Node): void {
        const index = this.spawnZone.indexOf(node);
        if (index > -1) {
            this.spawnZone.splice(index, 1);
            console.log(`SpawnTile: Removed ${node.name} from spawn zone. Total zones: ${this.spawnZone.length}`);
        } else {
            console.warn(`SpawnTile: Node ${node.name} không có trong spawn zone`);
        }
    }

    /**
     * Xóa tất cả spawn zones
     */
    public clearSpawnZones(): void {
        const count = this.spawnZone.length;
        this.spawnZone = [];
        console.log(`SpawnTile: Cleared all spawn zones (${count} zones removed)`);
    }

    /**
     * Lấy thông tin debug về spawn zones
     */
    public getSpawnZoneDebugInfo(): string {
        if (this.spawnZone.length === 0) {
            return "SpawnTile: No spawn zones configured (using current node)";
        }

        const zoneNames = this.spawnZone.map((node, index) => `${index}: ${node.name}`).join(', ');
        return `SpawnTile: ${this.spawnZone.length} spawn zones - [${zoneNames}]`;
    }

    /**
     * Kiểm tra tính hợp lệ của spawn zones
     */
    public validateSpawnZones(): boolean {
        let validCount = 0;

        for (let i = this.spawnZone.length - 1; i >= 0; i--) {
            const node = this.spawnZone[i];
            if (!node || !node.isValid) {
                console.warn(`SpawnTile: Removing invalid node at index ${i}`);
                this.spawnZone.splice(i, 1);
            } else if (!node.getComponent(UITransform)) {
                console.warn(`SpawnTile: Node ${node.name} at index ${i} không có UITransform, removing from spawn zone`);
                this.spawnZone.splice(i, 1);
            } else {
                validCount++;
            }
        }

        console.log(`SpawnTile: Validated spawn zones - ${validCount} valid zones remaining`);
        return validCount > 0;
    }
}


