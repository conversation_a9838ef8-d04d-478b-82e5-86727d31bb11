import { _decorator, Component, Sprite, tween } from 'cc';
import { Signal } from '../eventSystem/Signal';
import { GameManager } from '../core/GameManager';
import { GameState } from '../enum/Enums';
import { SoundManager } from '../core/SoundManager';
const { ccclass, property } = _decorator;

@ccclass('ProgressBarUI')
export class ProgressBarUI extends Component {
    @property(Sprite)
    progressBar: Sprite = null!;

    @property({ tooltip: "Thời gian animation (giây)" })
    animationDuration: number = 0.5;

    private _maxValue: number = 100;
    private _currentValue: number = 0;
    private _isAnimating: boolean = false;
    private _lastTriggeredValue: number = -1; // Để tránh trigger trùng lặp

    public onChangeValue: Signal<number> = new Signal();

    protected start(): void {
        this.initProgressBar();
    }

    /**
     * Khởi tạo progress bar
     */
    private initProgressBar(): void {
        if (this.progressBar) {
            // Thiết lập sprite là FILLED type với hướng ngang
            this.progressBar.type = Sprite.Type.FILLED;
            this.progressBar.fillType = Sprite.FillType.HORIZONTAL;
            this.progressBar.fillStart = 0;
            this.progressBar.fillRange = 0; // Bắt đầu từ 0%
        }

        this.setProgress(0, false); // Khởi tạo với giá trị 0, không có animation
    }

    /**
     * Thiết lập giá trị tối đa
     */
    public setMaxValue(maxValue: number): void {
        this._maxValue = Math.max(1, maxValue);
    }

    /**
     * Lấy giá trị tối đa
     */
    public getMaxValue(): number {
        return this._maxValue;
    }

    /**
     * Thiết lập giá trị hiện tại với animation
     */
    public setProgress(value: number, animated: boolean = true): void {
        const clampedValue = Math.max(0, Math.min(this._maxValue, value));

        if (animated && !this._isAnimating) {
            this.animateToValue(clampedValue);
        } else {
            const oldValue = this._currentValue;
            this._currentValue = clampedValue;
            this.updateProgressBar();
        }
    }

    /**
     * Lấy giá trị hiện tại
     */
    public getCurrentValue(): number {
        return this._currentValue;
    }

    /**
     * Lấy phần trăm hiện tại (0-1)
     */
    public getProgressPercent(): number {
        return this._currentValue / this._maxValue;
    }

    /**
     * Tăng giá trị với animation
     */
    public increaseProgress(amount: number, animated: boolean = true): void {
        this.setProgress(this._currentValue + amount, animated);
    }

    /**
     * Animation chuyển đổi giá trị
     */
    private animateToValue(targetValue: number): void {
        if (this._isAnimating) {
            return;
        }

        this._isAnimating = true;
        const startValue = this._currentValue;
        const valueRange = targetValue - startValue;

        tween({ progress: 0 })
            .to(this.animationDuration, { progress: 1 }, {
                easing: 'sineOut',
                onUpdate: (target: any) => {
                    const newValue = startValue + (valueRange * target.progress);
                    this._currentValue = newValue;
                    this.updateProgressBar();

                    // Không trigger signal trong onUpdate để tránh spam
                },
                onComplete: () => {
                    this._currentValue = targetValue;
                    this.updateProgressBar();
                    this._isAnimating = false;

                    // Kiểm tra khi value = maxValue
                    

                    // Chỉ trigger nếu giá trị khác với lần trigger trước
                    if (this._currentValue !== this._lastTriggeredValue) {
                        this._lastTriggeredValue = this._currentValue;
                        this.onChangeValue.trigger(this._currentValue);
                    }


                    if (this._currentValue === this._maxValue) {
                        console.log(`ProgressBarUI: Reached maximum value! Current: ${this._currentValue}, Max: ${this._maxValue}`);
                        GameManager.Instance.setGameState(GameState.Win);
                        SoundManager.Instance.playSfx(SoundManager.Instance.Win);
                    }
                }
            })
            .start();
    }

    /**
     * Cập nhật hiển thị progress bar
     */
    private updateProgressBar(): void {
        if (!this.progressBar) return;

        const progress = this.getProgressPercent();
        this.progressBar.fillRange = progress;
    }

    /**
     * Reset progress bar về 0
     */
    public resetProgress(animated: boolean = true): void {
        this.setProgress(0, animated);
    }

    /**
     * Thiết lập progress bar đầy (100%)
     */
    public setProgressFull(animated: boolean = true): void {
        this.setProgress(this._maxValue, animated);
    }

    /**
     * Kiểm tra xem có đang animation không
     */
    public isAnimating(): boolean {
        return this._isAnimating;
    }


}


