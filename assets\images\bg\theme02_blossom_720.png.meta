{"ver": "1.0.25", "importer": "image", "imported": true, "uuid": "9f9d6df7-76d8-4c8b-bffb-668467309a50", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "9f9d6df7-76d8-4c8b-bffb-668467309a50@6c48a", "displayName": "theme02_blossom_720", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "9f9d6df7-76d8-4c8b-bffb-668467309a50", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "9f9d6df7-76d8-4c8b-bffb-668467309a50@f9941", "displayName": "theme02_blossom_720", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 540, "height": 720, "rawWidth": 540, "rawHeight": 720, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-270, -360, 0, 270, -360, 0, -270, 360, 0, 270, 360, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 720, 540, 720, 0, 0, 540, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-270, -360, 0], "maxPos": [270, 360, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "9f9d6df7-76d8-4c8b-bffb-668467309a50@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": true, "hasAlpha": false, "redirect": "9f9d6df7-76d8-4c8b-bffb-668467309a50@f9941"}}