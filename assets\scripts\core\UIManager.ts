import { _decorator, Component, Node, sys, tween } from 'cc';
import { GameManager } from './GameManager';
import { GameState } from '../enum/Enums';
import { UIButton } from '../eventSystem/UIButton';
import { PopupCollection } from '../ui/PopupCollection';

const { ccclass, property } = _decorator;

@ccclass('UIManager')
export class UIManager extends Component {

    @property(Node)
    popupLose: Node = null!;

    @property(Node)
    popupWin: Node = null!;

    @property(Node)
    popupCollection: Node = null!;

    @property(UIButton)
    ctaButton: UIButton = null!;

    private panels: Node[] = [];

    protected onLoad(): void {
        this.initializePanels();
        GameManager.Instance.onStateChange.on(this.gameStateChangeCallback, this);

        this.ctaButton.InteractedEvent.on(this.callCTA, this);
    }

    private initializePanels(): void {

        this.panels = [
            this.popupLose,
            this.popupWin,
        ];

        this.panels.forEach(panel => {
            if (panel) {
                panel.active = false;
            }
        });
    }

    private showPanel(panel: Node): void {
        this.panels.forEach(item => {
            if (item) {
                item.active = (item === panel);
            }
        });
    }

    private gameStateChangeCallback(state: GameState): void {
        switch (state) {
            case GameState.Lose:
                this.showPanel(this.popupLose);
                this.callCTA();
                break;
            case GameState.Win:

                if (this.popupCollection) {
                    this.popupCollection.active = true;

                    tween(this.popupCollection)
                        .delay(1)
                        .call(() => {
                            this.popupCollection.getComponent(PopupCollection).hide();
                        })
                        .start();

                    tween(this.popupCollection)
                        .delay(1.3)
                        .call(() => {
                            this.showPanel(this.popupWin);
                            this.callCTA();
                        })

                        .start();
                }
                else {
                    this.showPanel(this.popupWin);
                    this.callCTA();
                }

                break;
        }
    }

    public callCTA() {
        console.log('openAppStore');
        const adNetwork = window['advChannels'];

        if (!adNetwork) return;

        switch (adNetwork) {
            case "Mintegral":
                window['install']?.();
                break;

            case "Unity":
            case "AppLovin":
                this.openAppStore();
                break;
        }
    }

    private openAppStore() {
        const isAndroid = sys.os === sys.OS.ANDROID;
        const storeFunction = isAndroid ?
            window["mraidOpenPlayStore"] :
            window["mraidOpenAppStore"];

        storeFunction?.();

        // console.log('openAppStore');
    }
}


