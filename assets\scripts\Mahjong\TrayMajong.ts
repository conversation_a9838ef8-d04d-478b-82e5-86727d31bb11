import { _decorator, Component, Node, tween, UITransform } from 'cc';
import { TileMahjong } from './TileMahjong';
import { Signal } from '../eventSystem/Signal';
import { SoundAdapter } from './SoundAdapter';
const { ccclass, property } = _decorator;

@ccclass('TrayMajong')
export class TrayMajong extends Component {

    @property(Node) private traySlotsParent: Node = null!;

    private traySlots: Node[] = [];
    private readonly MAX_SLOTS = 7;
    private readonly MATCH_COUNT = 3;
    private _holder: TileMahjong[] = [];
    private _undoStack: TileMahjong[] = [];

    public onStartCheckMatch = new Signal();
    public onPerformMatchCompleted = new Signal();

    // Getters
    get holder(): TileMahjong[] { return this._holder; }
    get isFull(): boolean { return this._holder.length >= this.MAX_SLOTS; }
    get isEmpty(): boolean { return this._holder.length === 0; }
    get canUndo(): boolean { return this._undoStack.length > 0; }

    onLoad() {
        this.initializeTraySlots();
        TileMahjong.onTileTap.on(this.onTileTapped, this);
    }

    protected onDestroy(): void {
        TileMahjong.onTileTap.off(this.onTileTapped);
    }

    private onTileTapped(tile: TileMahjong) {
        this.addTile(tile);
    }

    private initializeTraySlots() {
        if (this.traySlots.length === 0) {
            this.traySlots = this.traySlotsParent.children
                .sort((a, b) => {
                    const aMatch = a.name.match(/TraySlot_(\d+)/);
                    const bMatch = b.name.match(/TraySlot_(\d+)/);
                    return aMatch && bMatch ?
                        parseInt(aMatch[1]) - parseInt(bMatch[1]) :
                        a.position.x - b.position.x;
                })
                .slice(0, this.MAX_SLOTS);
        }
    }

    async addTile(tile: TileMahjong): Promise<boolean> {
        if (this.isFull) {
            console.warn("TrayMajong is full!");
            return false;
        }

        this._undoStack.push(tile);
        this._holder.push(tile);
        tile.setSelected(true);
        tile.isInTray = true;

        const targetSlot = this.traySlots[this._holder.length - 1];
        const worldPos = targetSlot.getWorldPosition();

        // Tìm node "contain" trong traySlot
        const containNode = targetSlot.getChildByName('contain');
        if (!containNode) {
            console.warn(`TrayMajong: Cannot find 'contain' node in traySlot`);
        }

        await tile.moveTo(worldPos, 0.5);

        SoundAdapter.instance.playClick();

        this.arrangeSlots();
        await this.checkMatches();

        return !this.isFull;
    }

    /**
     * Sắp xếp lại vị trí các tile trong khay
     */
    private arrangeSlots() {
        for (let i = 0; i < this._holder.length; i++) {
            const tile = this._holder[i];
            const targetSlot = this.traySlots[i];

            if (tile && targetSlot) {
                const worldPos = targetSlot.getWorldPosition();

                // Chuyển đổi world position thành local position của tile's parent
                const tileParent = tile.node.parent;
                if (tileParent) {
                    const parentUITransform = tileParent.getComponent(UITransform);
                    if (parentUITransform) {
                        const localPos = parentUITransform.convertToNodeSpaceAR(worldPos);

                        // Animation di chuyển mượt mà
                        tween(tile.node)
                            .to(0.2, { position: localPos })
                            .start();
                    }
                }
            }
        }
    }

    private async checkMatches(): Promise<void> {
        const tileCount: { [key: number]: TileMahjong[] } = {};

        // Đếm tile theo type (bỏ qua type -1)
        this._holder.forEach(tile => {
            if (tile.tileType !== -1) {
                (tileCount[tile.tileType] ||= []).push(tile);
            }
        });

        // Tìm và match loại tile đầu tiên có đủ 3 cái
        for (const type in tileCount) {
            const tiles = tileCount[type];
            if (tiles.length >= this.MATCH_COUNT) {
                await this.performMatch(tiles.slice(0, this.MATCH_COUNT));
                break;
            }
        }
    }

    private async performMatch(matchedTiles: TileMahjong[]): Promise<void> {
        console.log("TrayMajong: Performing match with", matchedTiles.length, "tiles");

        this.onStartCheckMatch?.trigger();

        SoundAdapter.instance.playMatch();

        // Animation biến mất
        await Promise.all(matchedTiles.map((tile, index) =>
            tile.playDisappearAnimation(index * 0.1)
        ));

        // Xóa tiles và cleanup
        matchedTiles.forEach(tile => {
            this.removeTileFromArrays(tile);
            tile.isInTray = false;
            tile.resetTile();
            tile.node.destroy();
        });

        this.arrangeSlots();
        this.onPerformMatchCompleted?.trigger();
    }

    private removeTileFromArrays(tile: TileMahjong): void {
        const holderIndex = this._holder.indexOf(tile);
        if (holderIndex !== -1) this._holder.splice(holderIndex, 1);

        const undoIndex = this._undoStack.indexOf(tile);
        if (undoIndex !== -1) this._undoStack.splice(undoIndex, 1);
    }

    /**
     * Chức năng Undo - lấy tile cuối cùng ra khỏi khay
     */
    undoLastTile(): TileMahjong | null {
        if (!this.canUndo) {
            return null;
        }

        // Lấy tile cuối cùng từ undo stack
        const lastTile = this._undoStack.pop();
        if (!lastTile) {
            return null;
        }

        // Xóa tile khỏi holder
        const index = this._holder.indexOf(lastTile);
        if (index !== -1) {
            this._holder.splice(index, 1);
        }

        // Reset trạng thái tray
        lastTile.isInTray = false;

        // Sắp xếp lại các tile còn lại
        this.arrangeSlots();

        return lastTile;
    }

    clearTray() {
        Promise.all(this._holder.map((tile, index) =>
            tile.playDisappearAnimation(index * 0.05)
        )).then(() => {
            this._holder.forEach(tile => {
                tile.isInTray = false;
                tile.resetTile();
                tile.node.destroy();
            });
            this._holder = [];
            this._undoStack = [];
        });
    }

    // Utility methods
    canAddTile(): boolean { return !this.isFull; }
    getTileAt(index: number): TileMahjong | null { return this._holder[index] || null; }
    getTileCount(): number { return this._holder.length; }
    isEmptyTray(): boolean { return this._holder.length === 0; }

    getTileTypeCount(): { [key: number]: number } {
        const count: { [key: number]: number } = {};
        this._holder.forEach(tile => {
            if (tile.tileType !== -1) {
                count[tile.tileType] = (count[tile.tileType] || 0) + 1;
            }
        });
        return count;
    }

    canMatch(): boolean {
        const typeCount = this.getTileTypeCount();
        return Object.keys(typeCount).some(type => typeCount[parseInt(type)] >= this.MATCH_COUNT);
    }

    shuffleTray() {
        if (this._holder.length <= 1) return;

        for (let i = this._holder.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this._holder[i], this._holder[j]] = [this._holder[j], this._holder[i]];
        }
        this.arrangeSlots();
    }

    getDebugInfo(): string {
        const typeCount = this.getTileTypeCount();
        const typeInfo = Object.keys(typeCount)
            .map(type => `Type${type}:${typeCount[parseInt(type)]}`)
            .join(', ');
        return `TrayMajong[${this._holder.length}/${this.MAX_SLOTS}] - ${typeInfo}`;
    }

}


