import { _decorator, CCInteger, Component, Node, tween, Vec3 } from 'cc';
import { ProgressBarUI } from './ProgressBarUI';
const { ccclass, property } = _decorator;

@ccclass('PopStarEffect')
export class PopStarEffect extends Component {
    @property({ type: [CCInteger], tooltip: "C<PERSON>c mốc điểm để active star" })
    targetScore: number[] = [];

    @property({ type: [Node], tooltip: "Các star node theo thứ tự" })
    starNodes: Node[] = [];

    @property({ tooltip: "Thời gian delay giữa các star (giây)" })
    starDelay: number = 0.2;

    @property({ tooltip: "Thời gian animation cho mỗi star (giây)" })
    starAnimationDuration: number = 0.5;

    @property({ tooltip: "Scale khi star xuất hiện" })
    starPopScale: number = 1.2;

    private _currentActiveStars: number = 0;
    private _isAnimating: boolean = false;
    private _progressBar: ProgressBarUI | null = null;
    private _onProgressChanged = (value: number) => {
        this.updateVisual(value);
    };

    protected start(): void {
        this._progressBar = this.getComponent(ProgressBarUI);
        if (this._progressBar) {
            this._progressBar.onChangeValue.on(this._onProgressChanged, this);
        }

        this.initStars();
    }

    protected onDestroy(): void {
        if (this._progressBar) {
            this._progressBar.onChangeValue.off(this._onProgressChanged);
        }
    }

    /**
     * Khởi tạo trạng thái ban đầu của các star
     */
    private initStars(): void {
        this.starNodes.forEach((star) => {
            if (star) {
                star.active = false;
                star.setScale(Vec3.ZERO);
            }
        });
        this._currentActiveStars = 0;
    }

    /**
     * Cập nhật hiển thị star dựa trên score
     */
    public updateVisual(score: number): void {
        if (this._isAnimating) return;

        console.log('Update visual with score:', score);

        const starsToActivate = this.calculateStarsToActivate(score);

        if (starsToActivate > this._currentActiveStars) {
            this.activateStarsSequentially(this._currentActiveStars, starsToActivate);
        } else if (starsToActivate < this._currentActiveStars) {
            this.deactivateStarsSequentially(starsToActivate, this._currentActiveStars);
        }
    }

    /**
     * Tính toán số star cần active dựa trên score
     */
    private calculateStarsToActivate(score: number): number {
        let starsToActivate = 0;

        for (let i = 0; i < this.targetScore.length; i++) {
            if (score >= this.targetScore[i]) {
                starsToActivate = i + 1;
            } else {
                break;
            }
        }

        return Math.min(starsToActivate, this.starNodes.length);
    }

    /**
     * Active các star theo thứ tự với animation
     */
    private activateStarsSequentially(fromIndex: number, toIndex: number): void {
        if (fromIndex >= toIndex) return;

        this._isAnimating = true;
        let currentIndex = fromIndex;

        const activateNextStar = () => {
            if (currentIndex >= toIndex) {
                this._currentActiveStars = toIndex;
                this._isAnimating = false;
                return;
            }

            const star = this.starNodes[currentIndex];
            if (star) {
                this.animateStarAppear(star, () => {
                    currentIndex++;
                    if (currentIndex < toIndex) {
                        this.scheduleOnce(activateNextStar, this.starDelay);
                    } else {
                        this._currentActiveStars = toIndex;
                        this._isAnimating = false;
                    }
                });
            } else {
                currentIndex++;
                activateNextStar();
            }
        };

        activateNextStar();
    }

    /**
     * Deactive các star theo thứ tự ngược
     */
    private deactivateStarsSequentially(fromIndex: number, toIndex: number): void {
        if (fromIndex >= toIndex) return;

        this._isAnimating = true;
        let currentIndex = toIndex - 1;

        const deactivateNextStar = () => {
            if (currentIndex < fromIndex) {
                this._currentActiveStars = fromIndex;
                this._isAnimating = false;
                return;
            }

            const star = this.starNodes[currentIndex];
            if (star) {
                this.animateStarDisappear(star, () => {
                    currentIndex--;
                    if (currentIndex >= fromIndex) {
                        this.scheduleOnce(deactivateNextStar, this.starDelay);
                    } else {
                        this._currentActiveStars = fromIndex;
                        this._isAnimating = false;
                    }
                });
            } else {
                currentIndex--;
                deactivateNextStar();
            }
        };

        deactivateNextStar();
    }

    /**
     * Animation star xuất hiện
     */
    private animateStarAppear(star: Node, callback?: () => void): void {
        star.active = true;
        star.setScale(Vec3.ZERO);

        tween(star)
            .to(this.starAnimationDuration * 0.6, { scale: new Vec3(this.starPopScale, this.starPopScale, 1) }, { easing: 'backOut' })
            .to(this.starAnimationDuration * 0.4, { scale: Vec3.ONE }, { easing: 'sineOut' })
            .call(() => {
                if (callback) callback();
            })
            .start();
    }

    /**
     * Animation star biến mất
     */
    private animateStarDisappear(star: Node, callback?: () => void): void {
        tween(star)
            .to(this.starAnimationDuration, { scale: Vec3.ZERO }, { easing: 'sineIn' })
            .call(() => {
                star.active = false;
                if (callback) callback();
            })
            .start();
    }

    /**
     * Thiết lập các mốc điểm mới
     */
    public setTargetScores(scores: number[]): void {
        this.targetScore = [...scores];
    }

    /**
     * Lấy số star hiện tại đang active
     */
    public getCurrentActiveStars(): number {
        return this._currentActiveStars;
    }

    /**
     * Kiểm tra xem có đang animation không
     */
    public isAnimating(): boolean {
        return this._isAnimating;
    }

    /**
     * Reset tất cả star về trạng thái ban đầu
     */
    public resetStars(): void {
        if (this._isAnimating) return;

        this.starNodes.forEach(star => {
            if (star) {
                star.active = false;
                star.setScale(Vec3.ZERO);
            }
        });
        this._currentActiveStars = 0;
    }

    /**
     * Force active một số lượng star nhất định (không animation)
     */
    public forceActivateStars(count: number): void {
        const clampedCount = Math.max(0, Math.min(count, this.starNodes.length));

        this.starNodes.forEach((star, index) => {
            if (star) {
                if (index < clampedCount) {
                    star.active = true;
                    star.setScale(Vec3.ONE);
                } else {
                    star.active = false;
                    star.setScale(Vec3.ZERO);
                }
            }
        });

        this._currentActiveStars = clampedCount;
    }
}


