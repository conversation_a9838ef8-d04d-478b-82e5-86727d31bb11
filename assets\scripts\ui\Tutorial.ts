import { _decorator, Component, Node, Vec2, Vec3 } from 'cc';
import { MapLoader } from '../game/Map/MapLoader';
import { Tray } from '../game/Tray/Tray';
const { ccclass, property } = _decorator;

@ccclass('Tutorial')
export class Tutorial extends Component {
    @property(Node) private hand: Node;

    @property(Node) private stepNodes: Node[] = [];
    @property(Vec2) private stepTilePos: Vec2[] = [];

    @property(Tray) private tray: Tray = null!;

    private mapLoader: MapLoader = null!;

    private currentStep = 0;

    protected onLoad(): void {
        this.hand.active = false;
        this.stepNodes.forEach(node => node.active = false);

        this.tray.onPerformMatchCompleted.on(this.doneTut, this);
    }

    public initialize(mapLoader: MapLoader) {
        console.log('Tutorial: Initialize');
        this.mapLoader = mapLoader;

        // Lấy tileTut từ level hiện tại ở layer 0 và set vào stepTilePos
        const tileTutPositions = this.getTileTutFromLevel(mapLoader);
        if (tileTutPositions.length > 0) {
            this.stepTilePos = tileTutPositions.map((pos: { x: number, y: number }) => new Vec2(pos.x, pos.y));
            console.log(`Tutorial: Set stepTilePos from tileTut:`, this.stepTilePos);
        }

        this.step1();
    }

    private step1() {
        

        this.stepNodes[0].active = true;

        if (this.stepTilePos.length > 0 && this.stepNodes.length > 0) {
            const tile0 = this.mapLoader.getTileAt(0, this.stepTilePos[0].x, this.stepTilePos[0].y);
            if (tile0) {
                this.hand.active = true;
                tile0.node.parent = this.stepNodes[0];
                console.log(`Tutorial: Set tile at layer 0 position (${this.stepTilePos[0].x}, ${this.stepTilePos[0].y}) to stepNodes[0]`);
                tile0.onTileTutTap.on(this.handleStep, this);

                const pos = tile0.node.worldPosition;
                this.hand.setWorldPosition(new Vec3(pos.x + 30, pos.y - 30, 0));
                tile0.showGlowEffect();
            } else {
                console.warn(`Tutorial: Cannot find tile at layer 0 position (${this.stepTilePos[0].x}, ${this.stepTilePos[0].y})`);
            }


        }
    }

    private step2() {
        
        this.hand.active = false;
        this.stepNodes[1].active = true;

        // Lấy tile từ layer 1 tại vị trí stepTilePos[1] và [2]
        if (this.stepTilePos.length > 2 && this.stepNodes.length > 1) {
            // Tile tại stepTilePos[1]
            const tile1 = this.mapLoader.getTileAt(0, this.stepTilePos[1].x, this.stepTilePos[1].y);
            if (tile1) {
                
                tile1.node.parent = this.stepNodes[1];
                console.log(`Tutorial: Set tile at layer 1 position (${this.stepTilePos[1].x}, ${this.stepTilePos[1].y}) to stepNodes[1]`);
                tile1.onTileTutTap.on(this.handleStep, this);
                tile1.showGlowEffect();
            } else {
                console.warn(`Tutorial: Cannot find tile at layer 1 position (${this.stepTilePos[1].x}, ${this.stepTilePos[1].y})`);
            }

            // Tile tại stepTilePos[2]
            const tile2 = this.mapLoader.getTileAt(0, this.stepTilePos[2].x, this.stepTilePos[2].y);
            if (tile2) {
                tile2.node.parent = this.stepNodes[1];
                console.log(`Tutorial: Set tile at layer 1 position (${this.stepTilePos[2].x}, ${this.stepTilePos[2].y}) to stepNodes[1]`);
                tile2.onTileTutTap.on(this.handleStep, this);
                tile2.showGlowEffect();
            } else {
                console.warn(`Tutorial: Cannot find tile at layer 1 position (${this.stepTilePos[2].x}, ${this.stepTilePos[2].y})`);
            }
        }
    }

    private handleStep() {
        this.currentStep++;

        if (this.currentStep === 1) {
            this.step2();
        }

    }

    private doneTut() {
        this.node.active = false;
    }

    /**
     * Lấy tileTut từ level hiện tại ở layer 0
     */
    private getTileTutFromLevel(mapLoader: MapLoader): { x: number, y: number }[] {
        try {
            // Lấy level data từ mapLoader
            const levelData = mapLoader['levelData'];
            if (!levelData || !levelData.json) {
                console.warn('Tutorial: Cannot access level data');
                return [];
            }

            const currentLevel = mapLoader.CurrentLevel;
            const levelsArray = levelData.json;

            if (currentLevel < 0 || currentLevel >= levelsArray.length) {
                console.warn(`Tutorial: Level index ${currentLevel} is out of range`);
                return [];
            }

            const level = levelsArray[currentLevel];
            if (!level || !level.tilemap || !Array.isArray(level.tilemap)) {
                console.warn('Tutorial: Invalid level structure');
                return [];
            }

            // Lấy layer 0 (layer đầu tiên)
            const layer0 = level.tilemap[0];
            if (!layer0 || !layer0.tileTut) {
                console.warn('Tutorial: Layer 0 or tileTut not found');
                return [];
            }

            console.log(`Tutorial: Found tileTut in level ${currentLevel}:`, layer0.tileTut);
            return layer0.tileTut;

        } catch (error) {
            console.error('Tutorial: Error getting tileTut from level:', error);
            return [];
        }
    }
}


