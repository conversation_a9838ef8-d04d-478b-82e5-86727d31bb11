import { _decorator, CCInteger, Component, Node, UITransform, tween } from 'cc';
import { TileMahjong } from '../TileMahjong';
const { ccclass, property } = _decorator;

@ccclass('MapLoaderPa1')
export class MapLoaderPa1 extends Component {
    @property([CCInteger]) private arrType: number[] = [];
    @property(Node) private container: Node = null!;

    @property(Node) private land: Node = null!;

    start() {
        this.loadMap();

        TileMahjong.onTileTap.on(this.onLand, this);
    }

    onDestroy() {
        TileMahjong.onTileTap.off(this.onLand);
    }

    private loadMap() {
        for (let i = 0; i < this.container.children.length; i++) {
            const type = this.arrType[i] || 0;
            const tile = this.container.children[i].getComponent(TileMahjong);

            if (tile) {
                tile.tileType = type;
            }
        }
    }

    private onLand(tile: Tile<PERSON>ah<PERSON>) {
        tile.node.parent = this.land;
    }
}


