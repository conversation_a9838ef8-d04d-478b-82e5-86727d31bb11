import { _decorator, CCInteger, Component, Node, UITransform, tween } from 'cc';
import { TileMahjong } from '../TileMahjong';
import { TutorialPa1 } from '../Tutorials/TutorialPa1';
import { SoundAdapter } from '../SoundAdapter';
const { ccclass, property } = _decorator;

@ccclass('MapLoaderPa1')
export class MapLoaderPa1 extends Component {
    @property([CCInteger]) private arrType: number[] = [];
    @property(Node) private container: Node = null!;
    @property(Node) private land: Node = null!;
    @property(TutorialPa1) private tutorial: TutorialPa1 = null!;

    private arrTile: TileMahjong[] = [];

    start() {
        TileMahjong.onTileTap.on(this.onLand, this);

        this.loadMap();

        this.hightLight();

        SoundAdapter.instance.playMusic();

    }

    onDestroy() {
        TileMahjong.onTileTap.off(this.onLand);
    }

    private loadMap() {
        for (let i = 0; i < this.container.children.length; i++) {
            const type = this.arrType[i] || 0;
            const tile = this.container.children[i].getComponent(TileMahjong);

            if (tile) {
                tile.tileType = type;
                this.arrTile.push(tile);
            }
        }
    }

    private onLand(tile: TileMahjong) {
        if (tile.tileType === this.tutorial.tutorialType) {
            this.tutorial.decreaseCount();
            this.tutorial.activeTutorial();
        }
        else {
            tile.node.parent = this.land;
        }

    }


    public getTileByType(type: number): TileMahjong[] | null {
        return this.arrTile.filter(tile => tile.tileType === type) || null;
    }

    private hightLight() {
        const type = this.tutorial.tutorialType;
        const arrTile = this.getTileByType(type);
        this.tutorial.arrTile = arrTile;


        if (arrTile) {
            arrTile.forEach(tile => {
                tile.node.parent = this.tutorial.contain;
            });
        }


        this.tutorial.timer = 3;
        this.tutorial.isActive = true;
    }
}


