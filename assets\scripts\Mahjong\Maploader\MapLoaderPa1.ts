import { _decorator, CCInteger, Component, Node } from 'cc';
import { TileMahjong } from '../TileMahjong';
const { ccclass, property } = _decorator;

@ccclass('MapLoaderPa1')
export class MapLoaderPa1 extends Component {
    @property([CCInteger]) private arrType: number[] = [];
    @property(Node) private container: Node = null!;

    private loadMap() {


        for (let i = 0; i < this.container.children.length; i++) {
            const type = this.arrType[i];
            const tile = this.container.children[i].getComponent(TileMahjong);

            if (tile) {
                tile.tileType = type;
            }
        }
    }







}


