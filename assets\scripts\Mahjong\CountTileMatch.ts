import { _decorator, CCInteger, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('CountTileMatch')
export class CountTileMatch extends Component {
    @property(CCInteger) private matchCount = 1;
    @property(CCInteger) private canTapCount = 7;

    public checkCount(): boolean {


        this.canTapCount--;

        if (this.canTapCount <= 0) return true;

        return false;
    }

    public match3() {
        this.matchCount--;
        this.matchCount = Math.max(0, this.matchCount);

        this.canTapCount = 3;
    }
}


