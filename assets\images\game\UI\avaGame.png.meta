{"ver": "1.0.25", "importer": "image", "imported": true, "uuid": "78ad1706-5693-4fcd-9eea-2c1ceea7e11f", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "78ad1706-5693-4fcd-9eea-2c1ceea7e11f@6c48a", "displayName": "avaGame", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "78ad1706-5693-4fcd-9eea-2c1ceea7e11f", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "78ad1706-5693-4fcd-9eea-2c1ceea7e11f@f9941", "displayName": "avaGame", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 269, "height": 254, "rawWidth": 269, "rawHeight": 254, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-134.5, -127, 0, 134.5, -127, 0, -134.5, 127, 0, 134.5, 127, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 254, 269, 254, 0, 0, 269, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-134.5, -127, 0], "maxPos": [134.5, 127, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "78ad1706-5693-4fcd-9eea-2c1ceea7e11f@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "fixAlphaTransparencyArtifacts": true, "hasAlpha": true, "redirect": "78ad1706-5693-4fcd-9eea-2c1ceea7e11f@f9941"}}