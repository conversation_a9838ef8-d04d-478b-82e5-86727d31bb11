[{"__type__": "cc.SceneAsset", "_name": "pa1", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "pa2", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 88}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 89}, "_id": "b29632b1-5136-4123-af57-6fedf733d3f6"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 9}, {"__id__": 69}, {"__id__": 73}, {"__id__": 76}], "_active": true, "_components": [{"__id__": 85}, {"__id__": 86}, {"__id__": 87}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 540, "y": 960, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e2AH+MkK9EQo+AiYUSvgSu"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "32u9XtHgRKBJS0ko3P/FrV"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 960, "_near": 0, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_cameraType": -1, "_trackingType": 0, "_id": "729r4a45NPnasKV9o46pes"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 6}, {"__id__": 7}, {"__id__": 8}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 3, "y": 3, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "97iGGR425Cvbey1KZDcLpI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 540, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "06gMih0sdPKamYsyg7lBXE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9f9d6df7-76d8-4c8b-bffb-668467309a50@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "95lP7U79FJw45oXiMRVsXs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_alignFlags": 5, "_target": null, "_left": 0, "_right": 0, "_top": -120, "_bottom": -120, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": "98/UuOT/dI/4ZwCU7nUlDh"}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 10}, {"__id__": 51}], "_active": true, "_components": [{"__id__": 67}, {"__id__": 68}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "18j84ltiJNto0NzVm+cz/J"}, {"__type__": "cc.Node", "_name": "Top", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 11}], "_active": true, "_components": [{"__id__": 49}, {"__id__": 50}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 760, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fdhls1jNdOdq+k9evqQ3/j"}, {"__type__": "cc.Node", "_name": "Tray", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 12}, {"__id__": 15}], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}, {"__id__": 48}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c4nd7efvpEXZthkNA/vitn"}, {"__type__": "cc.Node", "_name": "image", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 13}, {"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1.2, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4aEV2cxCRLqa7crJxgf5yM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 149}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "59FgLYmKNFCI2i2+3u26fg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5cab7e69-2eb6-407d-b8dd-3ed62309533f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e6B4fSVNFFw7YDsCChP2e8"}, {"__type__": "cc.Node", "_name": "positions", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [{"__id__": 16}, {"__id__": 20}, {"__id__": 24}, {"__id__": 28}, {"__id__": 32}, {"__id__": 36}, {"__id__": 40}], "_active": true, "_components": [{"__id__": 44}, {"__id__": 45}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.012000000000000455, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "18m2ECj8JGXYEhIFaS52zf"}, {"__type__": "cc.Node", "_name": "TraySlot_00", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 17}], "_active": true, "_components": [{"__id__": 19}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -428.57142857142856, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3cCQpfyqtIorznrxAFu8C+"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 18}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3enwkUCONIBYIdfV3cock8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "914A6coMZM8oT35LT8MLh1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 138.85714285714286, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fbbRH73thDRI+hI3wXmlph"}, {"__type__": "cc.Node", "_name": "TraySlot_01", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 21}], "_active": true, "_components": [{"__id__": 23}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -285.71428571428567, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "e5CJ3/IfFHmYl1s68Chj3E"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 22}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6dbsWRxRpHjYHu7VHKOsyK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f55mAZM+xBqb6jUUtghDLy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 138.85714285714286, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "29Nx40DChNaZy9PnAX3/nv"}, {"__type__": "cc.Node", "_name": "TraySlot_02", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 25}], "_active": true, "_components": [{"__id__": 27}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -142.85714285714278, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7foasgXIFMurfAtNJ111MN"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [{"__id__": 26}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "676SKi4h9IC7Pqamtgj776"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "92r6MTtWBBK4OQEbsBnZme"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 138.85714285714286, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "89V3D5Zb9ChbTkd0n0bzv7"}, {"__type__": "cc.Node", "_name": "TraySlot_03", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 29}], "_active": true, "_components": [{"__id__": 31}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 8.526512829121202e-14, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "36X6pISftPE7AgBsQShZVc"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 28}, "_children": [], "_active": true, "_components": [{"__id__": 30}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "37A0zP4bhMNYVHuyEia5zc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "aamgPuLRVB4rrDgO6KpJdX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 138.85714285714286, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "476JyV3/BNnq8QHAA3cP8m"}, {"__type__": "cc.Node", "_name": "TraySlot_04", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 33}], "_active": true, "_components": [{"__id__": 35}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 142.85714285714295, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "78koUqGvtAT54deLDbfh5Y"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 34}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2bLjoWeG5I+7xvXtQTsVr9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "07P/JbwfFKV77FRE3EsDoX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 138.85714285714286, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "43TSl+IcROjawZEx7pPPQ/"}, {"__type__": "cc.Node", "_name": "TraySlot_05", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 37}], "_active": true, "_components": [{"__id__": 39}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 285.71428571428584, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3elJ5vENtDAJKm831HsQZN"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 38}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bbwH3fACFFu6mTL5vFhKKs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "32UNa4DapCL6zsswzd1wb2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 138.85714285714286, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7fAH2ToI5OlYltRu/8BUqJ"}, {"__type__": "cc.Node", "_name": "TraySlot_06", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 41}], "_active": true, "_components": [{"__id__": 43}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 428.5714285714287, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3eMOMlKuNAxKI7rI+wlN1S"}, {"__type__": "cc.Node", "_name": "contain", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [], "_active": true, "_components": [{"__id__": 42}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "89/25O0QFH+om+7KgzS+tA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a1ErB5lRZMobdgitSv5M0e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 138.85714285714286, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5eAS25ekdA66ZlrHbmTwIe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2dVRRjN5ZOPaYCDAyXPXCS"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": false, "__prefab": null, "_resizeMode": 2, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 14, "_paddingRight": 14, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 4, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "7eLz6e8jhKg5SvjKdZFYxV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 149}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ebWpEW+j5KrKypPeiSI0Q9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5cab7e69-2eb6-407d-b8dd-3ed62309533f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "843sGtDzpM76O2sMDkNS12"}, {"__type__": "111eeSLfCtF1rykPovlD1kp", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "traySlotsParent": {"__id__": 15}, "_id": "f03QxZTb1Ft4Z3+2516o3s"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a0hXBylelPFJW6gUmfsnkz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 41, "_id": "d5kZABmvNG7ZKQ1RKVk7b0"}, {"__type__": "cc.Node", "_name": "SpawnNone", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 52}, {"__id__": 55}, {"__id__": 58}, {"__id__": 61}], "_active": true, "_components": [{"__id__": 64}, {"__id__": 65}, {"__id__": 66}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -798, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5bFXlYMkFA65Au/JE4tR7H"}, {"__type__": "cc.Node", "_name": "targetPos", "_objFlags": 0, "_parent": {"__id__": 51}, "_children": [], "_active": true, "_components": [{"__id__": 53}, {"__id__": 54}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2056, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f3VMxtG05AdaJjpkTAPr9o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "4bdFWIewtKDIa+FZo2yw5w"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_alignFlags": 1, "_target": {"__id__": 2}, "_left": 290, "_right": 290, "_top": -348, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 500, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": "15suyUQc5I96SS6q/ioQQo"}, {"__type__": "cc.Node", "_name": "SpawnZone_1", "_objFlags": 0, "_parent": {"__id__": 51}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 57}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 818.3169999999999, "y": -143.16500000000008, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "59sJv2rBJK65NQHzGoY5zC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 552.6859999999999, "height": 743.674}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6e4GMApYFByK3x3K4CnJGN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "__prefab": null, "_alignFlags": 37, "_target": null, "_left": 1081.9740000000002, "_right": -554.6599999999999, "_top": -66.67199999999994, "_bottom": -353.00200000000007, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 552.6859999999999, "_originalHeight": 743.674, "_alignMode": 2, "_lockFlags": 5, "_id": "b5v99TzylF/4zaTjzkgR2U"}, {"__type__": "cc.Node", "_name": "SpawnZone_2", "_objFlags": 0, "_parent": {"__id__": 51}, "_children": [], "_active": true, "_components": [{"__id__": 59}, {"__id__": 60}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -824.312, "y": -230.3135000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "397Dehme9PpLecBcmfncgh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 552.6859999999999, "height": 917.971}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "250sEjWTNAU5jWBiinYQdX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": -560.655, "_right": 1087.969, "_top": -66.67199999999994, "_bottom": -527.2990000000001, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 552.6859999999999, "_originalHeight": 917.971, "_alignMode": 2, "_lockFlags": 45, "_id": "7frhUwSoVDR7iOr6ywxM5p"}, {"__type__": "cc.Node", "_name": "SpawnZone_3", "_objFlags": 0, "_parent": {"__id__": 51}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 63}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -58.71600000000012, "y": -457.989, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "acykN/WaBL+K7Ad9CifILW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 2083.8779999999997, "height": 555.716}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "10ms8eG/ZNS7ow9M2DlbE3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": -560.655, "_right": -443.22299999999984, "_top": 342.131, "_bottom": -573.847, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2083.8779999999997, "_originalHeight": 555.716, "_alignMode": 2, "_lockFlags": 45, "_id": "61Wlv7bkxNOKnvFMHs55V+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 324}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c4+50Jh0lGfL49Ipd46c3e"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 910, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 4, "_id": "dbKvRtlCBLXZ3M/2zBQCMQ"}, {"__type__": "c9f51UUM7tIIo3L7Gg5rMHZ", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "tilePrefab": {"__uuid__": "34f4f1e8-356a-45d8-a225-0d987d47e857", "__expectedType__": "cc.Prefab"}, "delay": 1.5, "tileType": 4, "count": 100, "randomY": false, "randomTargetX": true, "randomTargetY": true, "spawnZone": [{"__id__": 55}, {"__id__": 58}, {"__id__": 61}, {"__id__": 61}, {"__id__": 61}, {"__id__": 61}], "targetPos": {"__id__": 52}, "_id": "29zDSeRYhKzYsfsi64CQ33"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "adtYVi1hBHlJ5yMRFOfbM6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 45, "_id": "95jDrFLm1HO4WfOfBBz2Bf"}, {"__type__": "cc.Node", "_name": "touchZone", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": false, "_components": [{"__id__": 70}, {"__id__": 71}, {"__id__": 72}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "17BwyrwUBAdqqoB4qRKPUe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b25FDWXuRKeJ10VAOkkFRc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "000CPqa6ZDeq2wqAJqfy0/"}, {"__type__": "8c2e3Bq3+tHbIhK6acZcV17", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "__prefab": null, "_id": "00radRpR5IxayiXNhUMOQv"}, {"__type__": "cc.Node", "_name": "Gamecontrol", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 74}, {"__id__": 75}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fe6jsIhGpD8K8V8RbmaJ3V"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c0xgtEtgtIprKqJWhL6AQG"}, {"__type__": "56cd8OMfuVFvrZy2BHf/8Tc", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "spawnTile": {"__id__": 66}, "trayMajong": {"__id__": 48}, "_id": "b6J7aAeKtBSLrd8V9YECtl"}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 77}, {"__id__": 80}], "_active": true, "_components": [{"__id__": 83}, {"__id__": 84}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "56SoIfQkNKq66nFAnDTqvC"}, {"__type__": "cc.Node", "_name": "MusicSource", "_objFlags": 0, "_parent": {"__id__": 76}, "_children": [], "_active": true, "_components": [{"__id__": 78}, {"__id__": 79}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d5J4U98ARPK53Bgt0qJvEd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "37fVyJYmdHuJDjh3EYLYjr"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": null, "_clip": null, "_loop": true, "_playOnAwake": false, "_volume": 1, "_id": "0ahmYOrENBho2zNEQAX+KL"}, {"__type__": "cc.Node", "_name": "AudioSource", "_objFlags": 0, "_parent": {"__id__": 76}, "_children": [], "_active": true, "_components": [{"__id__": 81}, {"__id__": 82}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "26/LfMNuBKWawSHfEAlb/u"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9fbv0LNxZBLJI+FSvNlgum"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "_clip": null, "_loop": false, "_playOnAwake": false, "_volume": 1, "_id": "3fEC+Fs65PHLmYMSnOYKZM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "be7HprRt5JaaCrImgyED8A"}, {"__type__": "2aa7f5saRFNd4m4Zw/OvNfv", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "__prefab": null, "music": {"__uuid__": "dabdb5c8-7925-429e-acd4-c0624c91acda", "__expectedType__": "cc.AudioClip"}, "click": {"__uuid__": "d9ffc87e-4e19-4d00-a409-a1ed211993ba", "__expectedType__": "cc.AudioClip"}, "match": {"__uuid__": "b882be2f-f634-4c51-acbc-59a24d48cab8", "__expectedType__": "cc.AudioClip"}, "bubble_pop": {"__uuid__": "38201bd6-e702-4c61-aff7-6b8958dc7f99", "__expectedType__": "cc.AudioClip"}, "musicSource": {"__id__": 79}, "sfxSource": {"__id__": 82}, "_id": "894GD5/oBIhKZ7YDZOg1cZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "72Y1PwcF1FcLwC29G9e/1O"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "b6g+tjzV5HXqG+J8YhL25G"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 5.684341886080802e-14, "_bottom": 5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "b9JcaOzJtP0Jqf49B4+k4h"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "b29632b1-5136-4123-af57-6fedf733d3f6", "instance": null, "targetOverrides": null}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 90}, "shadows": {"__id__": 91}, "_skybox": {"__id__": 92}, "fog": {"__id__": 93}, "octree": {"__id__": 94}, "lightProbeInfo": {"__id__": 95}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null}]