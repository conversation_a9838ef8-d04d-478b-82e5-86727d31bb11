import { _decorator, Component, Node } from 'cc';
import { Signal } from '../eventSystem/Signal';
import { GameState } from '../enum/Enums';
const { ccclass, property } = _decorator;

@ccclass('GameManager')
export class GameManager extends Component {

    private _currentGameState: GameState = GameState.Loading;

    public onStateChange: Signal<GameState> = new Signal();

    private static _instance: GameManager | null = null;

    public static get Instance(): GameManager {
        if (!this._instance) {
            console.error("GameManager instance is not yet available.");
        }
        return this._instance!;
    }

    protected onLoad(): void {
        GameManager._instance = this;
    }

   

    public get currentGameState(): GameState {
        return this._currentGameState;
    }

    public setGameState(state: GameState): void {
        this._currentGameState = state;
        this.onStateChange?.trigger(this._currentGameState);
    }

}


